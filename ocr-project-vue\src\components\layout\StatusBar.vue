<template>
  <footer class="status-bar">
    <div class="status-left">
      <div class="workspace-info" v-if="hasWorkspace">
        <el-icon><Folder /></el-icon>
        <span>{{ workspaceName }}</span>
        <el-divider direction="vertical" />
        <span>{{ imageCount }} 张图片</span>
        <span>{{ annotationCount }} 个标注</span>
      </div>
      <div v-else class="no-workspace">
        <span>未打开工作区</span>
      </div>
    </div>
    
    <div class="status-center">
      <div class="current-tool">
        <el-icon>
          <component :is="currentToolIcon" />
        </el-icon>
        <span>{{ currentToolLabel }}</span>
      </div>
      
      <el-divider direction="vertical" />
      
      <div class="selection-info" v-if="hasSelection">
        <span>已选择 {{ selectedCount }} 个标注</span>
      </div>
      <div v-else class="no-selection">
        <span>未选择标注</span>
      </div>
    </div>
    
    <div class="status-right">
      <div class="canvas-info" v-if="currentImage">
        <span>{{ Math.round(canvasScale * 100) }}%</span>
        <el-divider direction="vertical" />
        <span>{{ currentImage.size.width }}×{{ currentImage.size.height }}</span>
        <el-divider direction="vertical" />
        <span>{{ formatCoordinate(mousePosition) }}</span>
      </div>
      
      <el-divider direction="vertical" />
      
      <div class="save-status">
        <div v-if="hasUnsavedChanges" class="unsaved">
          <el-icon><Warning /></el-icon>
          <span>未保存</span>
        </div>
        <div v-else-if="lastSaveTime" class="saved">
          <el-icon><Check /></el-icon>
          <span>{{ formatSaveTime(lastSaveTime) }}</span>
        </div>
        <div v-else class="no-save">
          <span>未保存过</span>
        </div>
      </div>
      
      <el-divider direction="vertical" />
      
      <div class="mode-indicator">
        <el-tag :type="mode === 'annotation' ? 'primary' : 'success'" size="small">
          {{ mode === 'annotation' ? '标注模式' : '质检模式' }}
        </el-tag>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { 
  Folder, Warning, Check,
  Pointer, Document, List, Edit, Picture, ChatLineRound
} from '@element-plus/icons-vue'
import { useAppStore } from '@/stores/app'
import { useImageStore } from '@/stores/image'
import { useAnnotationStore } from '@/stores/annotation'
import { useDataStore } from '@/stores/data'
import { formatDateTime } from '@/utils/common'
import type { Point, Tool } from '@/types'

// Store
const appStore = useAppStore()
const imageStore = useImageStore()
const annotationStore = useAnnotationStore()
const dataStore = useDataStore()

// 响应式数据
const mousePosition = ref<Point>({ x: 0, y: 0 })

// 计算属性
const mode = computed(() => appStore.mode)
const currentTool = computed(() => appStore.currentTool)

const hasWorkspace = computed(() => dataStore.hasWorkspace)
const workspaceName = computed(() => dataStore.workspaceName)
const imageCount = computed(() => dataStore.imageCount)
const annotationCount = computed(() => dataStore.annotationCount)
const hasUnsavedChanges = computed(() => dataStore.hasUnsavedChanges)
const lastSaveTime = computed(() => dataStore.lastSaveTime)

const currentImage = computed(() => imageStore.currentImage)
const canvasScale = computed(() => imageStore.canvasScale)

const hasSelection = computed(() => annotationStore.hasSelection)
const selectedCount = computed(() => annotationStore.selectedAnnotations.length)

// 工具图标和标签映射
const toolConfig: Record<Tool, { icon: any; label: string }> = {
  'select': { icon: Pointer, label: '选择工具' },
  'main-question': { icon: Document, label: '大题工具' },
  'sub-question': { icon: List, label: '小题工具' },
  'answer-area': { icon: Edit, label: '答题区域工具' },
  'image-area': { icon: Picture, label: '配图区域工具' },
  'question-text': { icon: ChatLineRound, label: '题干文字工具' },
  'pan': { icon: Pointer, label: '平移工具' },
  'zoom': { icon: Pointer, label: '缩放工具' }
}

const currentToolIcon = computed(() => toolConfig[currentTool.value]?.icon || Pointer)
const currentToolLabel = computed(() => toolConfig[currentTool.value]?.label || '未知工具')

// 方法
const formatCoordinate = (point: Point): string => {
  return `${Math.round(point.x)}, ${Math.round(point.y)}`
}

const formatSaveTime = (time: Date): string => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  
  if (diff < 60000) { // 小于1分钟
    return '刚刚保存'
  } else if (diff < 3600000) { // 小于1小时
    const minutes = Math.floor(diff / 60000)
    return `${minutes}分钟前保存`
  } else if (diff < 86400000) { // 小于1天
    const hours = Math.floor(diff / 3600000)
    return `${hours}小时前保存`
  } else {
    return formatDateTime(time).split(' ')[0] // 只显示日期
  }
}

const handleMouseMove = (event: MouseEvent) => {
  // 将屏幕坐标转换为图片坐标
  if (currentImage.value) {
    const rect = (event.target as HTMLElement)?.getBoundingClientRect()
    if (rect) {
      const screenPoint = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      }
      mousePosition.value = imageStore.screenToImage(screenPoint)
    }
  }
}

// 生命周期
onMounted(() => {
  // 监听鼠标移动事件来更新坐标显示
  document.addEventListener('mousemove', handleMouseMove)
})

onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove)
})
</script>

<style lang="scss" scoped>
.status-bar {
  height: var(--status-bar-height);
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  @include flex-between;
  padding: 0 var(--spacing-md);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  
  .status-left, .status-center, .status-right {
    @include flex-start;
    gap: var(--spacing-sm);
  }
  
  .status-left {
    flex: 1;
    
    .workspace-info {
      @include flex-start;
      gap: var(--spacing-xs);
      
      .el-icon {
        color: var(--primary-color);
      }
      
      span {
        white-space: nowrap;
      }
    }
    
    .no-workspace {
      color: var(--text-light);
    }
  }
  
  .status-center {
    flex: 1;
    justify-content: center;
    
    .current-tool {
      @include flex-start;
      gap: var(--spacing-xs);
      
      .el-icon {
        color: var(--secondary-color);
      }
    }
    
    .selection-info {
      color: var(--success-color);
    }
    
    .no-selection {
      color: var(--text-light);
    }
  }
  
  .status-right {
    flex: 1;
    justify-content: flex-end;
    
    .canvas-info {
      @include flex-start;
      gap: var(--spacing-xs);
      
      span {
        white-space: nowrap;
      }
    }
    
    .save-status {
      .unsaved {
        @include flex-start;
        gap: var(--spacing-xs);
        color: var(--warning-color);
        
        .el-icon {
          animation: pulse 2s infinite;
        }
      }
      
      .saved {
        @include flex-start;
        gap: var(--spacing-xs);
        color: var(--success-color);
      }
      
      .no-save {
        color: var(--text-light);
      }
    }
    
    .mode-indicator {
      .el-tag {
        font-size: var(--font-size-xs);
      }
    }
  }
}

.el-divider--vertical {
  height: 16px;
  margin: 0 var(--spacing-xs);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
