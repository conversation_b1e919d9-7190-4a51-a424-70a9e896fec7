<template>
  <aside class="layout-sidebar" :class="{ collapsed: sidebarCollapsed }">
    <div class="sidebar-header">
      <h3 v-if="!sidebarCollapsed">工具面板</h3>
      <el-button 
        @click="toggleSidebar" 
        size="small" 
        :icon="sidebarCollapsed ? ArrowRight : ArrowLeft"
        circle
      />
    </div>
    
    <div class="sidebar-content" v-if="!sidebarCollapsed">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 图片列表 -->
        <el-tab-pane label="图片" name="images">
          <div class="image-list">
            <div class="list-header">
              <span class="count">共 {{ imageCount }} 张</span>
              <el-button-group size="small">
                <el-button @click="openImages" :icon="FolderOpened" />
                <el-button @click="openFolder" :icon="Folder" />
              </el-button-group>
            </div>
            
            <div class="list-content scrollbar">
              <div 
                v-for="(image, index) in images" 
                :key="image.id"
                class="image-item"
                :class="{ active: index === currentImageIndex }"
                @click="setCurrentImage(index)"
              >
                <div class="image-thumbnail">
                  <el-icon><Picture /></el-icon>
                </div>
                <div class="image-info">
                  <div class="image-name" :title="image.name">{{ image.name }}</div>
                  <div class="image-meta">
                    <span>{{ image.annotations.length }} 个标注</span>
                    <span>{{ formatImageSize(image.size) }}</span>
                  </div>
                </div>
                <div class="image-actions">
                  <el-button 
                    @click.stop="removeImage(image.id)" 
                    size="small" 
                    type="danger" 
                    :icon="Delete"
                    circle
                  />
                </div>
              </div>
              
              <div v-if="imageCount === 0" class="empty-state">
                <el-icon><Picture /></el-icon>
                <p>暂无图片</p>
                <el-button @click="openImages" type="primary" size="small">
                  添加图片
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <!-- 标注工具 -->
        <el-tab-pane label="工具" name="tools">
          <div class="tool-panel">
            <div class="tool-section">
              <h4>标注工具</h4>
              <div class="tool-grid">
                <div 
                  v-for="tool in annotationTools" 
                  :key="tool.type"
                  class="tool-item"
                  :class="{ active: currentTool === tool.type }"
                  @click="setTool(tool.type)"
                >
                  <el-icon>
                    <component :is="tool.icon" />
                  </el-icon>
                  <span>{{ tool.label }}</span>
                  <small>{{ tool.shortcut }}</small>
                </div>
              </div>
            </div>
            
            <div class="tool-section">
              <h4>视图工具</h4>
              <div class="view-controls">
                <el-row :gutter="8">
                  <el-col :span="12">
                    <el-button @click="zoomIn" size="small" block>
                      <el-icon><ZoomIn /></el-icon>
                      放大
                    </el-button>
                  </el-col>
                  <el-col :span="12">
                    <el-button @click="zoomOut" size="small" block>
                      <el-icon><ZoomOut /></el-icon>
                      缩小
                    </el-button>
                  </el-col>
                </el-row>
                
                <el-button @click="resetZoom" size="small" block class="mt-2">
                  <el-icon><FullScreen /></el-icon>
                  适应画布
                </el-button>
                
                <div class="zoom-info">
                  缩放: {{ Math.round(canvasScale * 100) }}%
                </div>
              </div>
            </div>
            
            <div class="tool-section">
              <h4>快速操作</h4>
              <div class="quick-actions">
                <el-button @click="selectAll" size="small" block>
                  <el-icon><Select /></el-icon>
                  全选
                </el-button>
                <el-button @click="clearSelection" size="small" block>
                  <el-icon><Close /></el-icon>
                  取消选择
                </el-button>
                <el-button 
                  @click="deleteSelected" 
                  size="small" 
                  type="danger" 
                  block
                  :disabled="!hasSelection"
                >
                  <el-icon><Delete /></el-icon>
                  删除选中
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <!-- 设置 -->
        <el-tab-pane label="设置" name="settings">
          <div class="settings-panel">
            <div class="setting-group">
              <h4>画布设置</h4>
              <el-form size="small" label-position="top">
                <el-form-item label="显示网格">
                  <el-switch v-model="showGrid" />
                </el-form-item>
                <el-form-item label="显示标尺">
                  <el-switch v-model="showRuler" />
                </el-form-item>
                <el-form-item label="吸附网格">
                  <el-switch v-model="snapToGrid" />
                </el-form-item>
              </el-form>
            </div>
            
            <div class="setting-group">
              <h4>标注设置</h4>
              <el-form size="small" label-position="top">
                <el-form-item label="显示标签">
                  <el-switch v-model="showLabels" />
                </el-form-item>
                <el-form-item label="线条宽度">
                  <el-slider v-model="strokeWidth" :min="1" :max="5" />
                </el-form-item>
                <el-form-item label="字体大小">
                  <el-slider v-model="fontSize" :min="10" :max="20" />
                </el-form-item>
              </el-form>
            </div>
            
            <div class="setting-group">
              <h4>自动保存</h4>
              <el-form size="small" label-position="top">
                <el-form-item label="启用自动保存">
                  <el-switch v-model="autoSave" />
                </el-form-item>
                <el-form-item label="保存间隔(秒)" v-if="autoSave">
                  <el-input-number 
                    v-model="saveInterval" 
                    :min="10" 
                    :max="300" 
                    :step="10"
                  />
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  ArrowLeft, ArrowRight, Picture, FolderOpened, Folder, Delete,
  ZoomIn, ZoomOut, FullScreen, Select, Close,
  Pointer, Document, List, Edit, ChatLineRound
} from '@element-plus/icons-vue'
import { useAppStore } from '@/stores/app'
import { useImageStore } from '@/stores/image'
import { useAnnotationStore } from '@/stores/annotation'
import type { Tool, Size } from '@/types'

// Store
const appStore = useAppStore()
const imageStore = useImageStore()
const annotationStore = useAnnotationStore()

// 响应式数据
const activeTab = ref('images')

// 计算属性
const sidebarCollapsed = computed(() => appStore.sidebarCollapsed)
const currentTool = computed(() => appStore.currentTool)

const images = computed(() => imageStore.images)
const imageCount = computed(() => imageStore.imageCount)
const currentImageIndex = computed(() => imageStore.currentImageIndex)
const canvasScale = computed(() => imageStore.canvasScale)

const hasSelection = computed(() => annotationStore.hasSelection)

// 设置相关的计算属性
const showGrid = computed({
  get: () => appStore.settings.canvas.showGrid,
  set: (value) => appStore.updateSettings({ canvas: { ...appStore.settings.canvas, showGrid: value } })
})

const showRuler = computed({
  get: () => appStore.settings.canvas.showRuler,
  set: (value) => appStore.updateSettings({ canvas: { ...appStore.settings.canvas, showRuler: value } })
})

const snapToGrid = computed({
  get: () => appStore.settings.canvas.snapToGrid,
  set: (value) => appStore.updateSettings({ canvas: { ...appStore.settings.canvas, snapToGrid: value } })
})

const showLabels = computed({
  get: () => appStore.settings.annotation.showLabels,
  set: (value) => appStore.updateSettings({ annotation: { ...appStore.settings.annotation, showLabels: value } })
})

const strokeWidth = computed({
  get: () => appStore.settings.annotation.strokeWidth,
  set: (value) => appStore.updateSettings({ annotation: { ...appStore.settings.annotation, strokeWidth: value } })
})

const fontSize = computed({
  get: () => appStore.settings.annotation.fontSize,
  set: (value) => appStore.updateSettings({ annotation: { ...appStore.settings.annotation, fontSize: value } })
})

const autoSave = computed({
  get: () => appStore.settings.general.autoSave,
  set: (value) => appStore.updateSettings({ general: { ...appStore.settings.general, autoSave: value } })
})

const saveInterval = computed({
  get: () => appStore.settings.general.saveInterval / 1000,
  set: (value) => appStore.updateSettings({ general: { ...appStore.settings.general, saveInterval: value * 1000 } })
})

// 标注工具配置
const annotationTools = [
  { type: 'select', label: '选择', icon: Pointer, shortcut: 'V' },
  { type: 'main-question', label: '大题', icon: Document, shortcut: 'Q' },
  { type: 'sub-question', label: '小题', icon: List, shortcut: 'W' },
  { type: 'answer-area', label: '答题区域', icon: Edit, shortcut: 'E' },
  { type: 'image-area', label: '配图区域', icon: Picture, shortcut: 'R' },
  { type: 'question-text', label: '题干文字', icon: ChatLineRound, shortcut: 'T' }
]

// 方法
const toggleSidebar = () => {
  appStore.toggleSidebar()
}

const setTool = (tool: Tool) => {
  appStore.setTool(tool)
}

const setCurrentImage = (index: number) => {
  imageStore.setCurrentImage(index)
}

const removeImage = (id: string) => {
  imageStore.removeImage(id)
}

const openImages = async () => {
  try {
    await imageStore.openImages()
  } catch (error) {
    console.error('Failed to open images:', error)
  }
}

const openFolder = async () => {
  try {
    await imageStore.openFolder()
  } catch (error) {
    console.error('Failed to open folder:', error)
  }
}

const zoomIn = () => {
  imageStore.zoomIn()
}

const zoomOut = () => {
  imageStore.zoomOut()
}

const resetZoom = () => {
  imageStore.fitToCanvas()
}

const selectAll = () => {
  annotationStore.selectAll()
}

const clearSelection = () => {
  annotationStore.clearSelection()
}

const deleteSelected = () => {
  annotationStore.deleteSelected()
}

const formatImageSize = (size: Size): string => {
  if (!size.width || !size.height) return '未知尺寸'
  return `${size.width}×${size.height}`
}
</script>

<style lang="scss" scoped>
.layout-sidebar {
  width: var(--sidebar-width);
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  transition: var(--transition-normal);
  
  &.collapsed {
    width: 50px;
  }
  
  .sidebar-header {
    height: 50px;
    @include flex-between;
    padding: 0 var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    
    h3 {
      margin: 0;
      font-size: var(--font-size-md);
      color: var(--text-color);
    }
  }
  
  .sidebar-content {
    flex: 1;
    overflow: hidden;
    
    .el-tabs {
      height: 100%;
      
      :deep(.el-tabs__content) {
        height: calc(100% - 40px);
        overflow: hidden;
      }
      
      :deep(.el-tab-pane) {
        height: 100%;
        overflow: hidden;
      }
    }
  }
}

.image-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .list-header {
    @include flex-between;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
    
    .count {
      font-size: var(--font-size-sm);
      color: var(--text-secondary);
    }
  }
  
  .list-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-sm);
    
    .image-item {
      @include flex-start;
      gap: var(--spacing-sm);
      padding: var(--spacing-sm);
      border-radius: var(--border-radius-small);
      cursor: pointer;
      transition: var(--transition-fast);
      margin-bottom: var(--spacing-xs);
      
      &:hover {
        background: var(--bg-color);
      }
      
      &.active {
        background: var(--secondary-color);
        color: white;
      }
      
      .image-thumbnail {
        width: 40px;
        height: 40px;
        @include flex-center;
        background: var(--bg-color);
        border-radius: var(--border-radius-small);
        
        .el-icon {
          font-size: 20px;
          color: var(--text-secondary);
        }
      }
      
      .image-info {
        flex: 1;
        min-width: 0;
        
        .image-name {
          font-size: var(--font-size-sm);
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .image-meta {
          font-size: var(--font-size-xs);
          color: var(--text-light);
          margin-top: 2px;
          
          span {
            margin-right: var(--spacing-sm);
          }
        }
      }
      
      .image-actions {
        opacity: 0;
        transition: var(--transition-fast);
      }
      
      &:hover .image-actions {
        opacity: 1;
      }
    }
    
    .empty-state {
      @include flex-center;
      flex-direction: column;
      gap: var(--spacing-md);
      padding: var(--spacing-xxl);
      color: var(--text-secondary);
      
      .el-icon {
        font-size: 48px;
      }
    }
  }
}

.tool-panel {
  padding: var(--spacing-md);
  
  .tool-section {
    margin-bottom: var(--spacing-lg);
    
    h4 {
      margin: 0 0 var(--spacing-md) 0;
      font-size: var(--font-size-sm);
      color: var(--text-color);
    }
    
    .tool-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-sm);
      
      .tool-item {
        @include flex-center;
        flex-direction: column;
        gap: var(--spacing-xs);
        padding: var(--spacing-md);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-small);
        cursor: pointer;
        transition: var(--transition-fast);
        
        &:hover {
          border-color: var(--secondary-color);
        }
        
        &.active {
          background: var(--secondary-color);
          color: white;
          border-color: var(--secondary-color);
        }
        
        span {
          font-size: var(--font-size-xs);
          font-weight: 500;
        }
        
        small {
          font-size: 10px;
          opacity: 0.7;
        }
      }
    }
    
    .view-controls, .quick-actions {
      .mt-2 {
        margin-top: var(--spacing-sm);
      }
      
      .zoom-info {
        text-align: center;
        font-size: var(--font-size-xs);
        color: var(--text-secondary);
        margin-top: var(--spacing-sm);
      }
    }
  }
}

.settings-panel {
  padding: var(--spacing-md);
  
  .setting-group {
    margin-bottom: var(--spacing-lg);
    
    h4 {
      margin: 0 0 var(--spacing-md) 0;
      font-size: var(--font-size-sm);
      color: var(--text-color);
    }
  }
}
</style>
