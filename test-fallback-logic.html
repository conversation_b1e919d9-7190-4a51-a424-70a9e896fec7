<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题干文字Fallback逻辑测试</title>
</head>
<body>
    <h1>题干文字Fallback逻辑测试</h1>
    
    <div id="test-results"></div>
    
    <script src="js/utils.js"></script>
    <script src="js/data-manager.js"></script>
    
    <script>
        // 测试数据1：从JSON加载的大题（没有题干文字框）
        const testJSON = {
            "大题1": {
                "坐标": [[55, 750], [786, 1139]],
                "题型": "填空题",
                "题干文字": "这是从JSON加载的题干文字",
                "题目是否带配图": "否"
            }
        };
        
        // 创建DataManager实例
        const dataManager = new DataManager();
        
        // 测试1: JSON转标注（模拟加载JSON文件）
        console.log("测试1: JSON转标注");
        const annotations = dataManager.parseJSONToAnnotations(testJSON);
        console.log("生成的标注:", annotations);
        
        // 验证大题标注的content是否包含题干文字
        const mainQuestion = annotations.find(ann => ann.type === 'main-question');
        const hasQuestionText = annotations.find(ann => ann.type === 'question-text');
        
        console.log("大题标注:", mainQuestion);
        console.log("题干文字框:", hasQuestionText);
        
        // 测试2: 标注转JSON（验证数据一致性）
        console.log("\n测试2: 标注转JSON");
        const generatedJSON = dataManager.generateJSONFromAnnotations(
            { name: "test.jpg" },
            annotations,
            {}
        );
        console.log("重新生成的JSON:", generatedJSON);
        
        // 验证结果
        const results = document.getElementById('test-results');
        let html = '<h2>测试结果</h2>';
        
        // 检查大题是否正确保存了题干文字
        if (mainQuestion && mainQuestion.attributes.content === "这是从JSON加载的题干文字") {
            html += '<p style="color: green;">✓ 从JSON加载时，题干文字正确保存到大题的content中</p>';
            html += `<p>大题content: "${mainQuestion.attributes.content}"</p>`;
        } else {
            html += '<p style="color: red;">✗ 从JSON加载时，题干文字未正确保存</p>';
            html += `<p>期望: "这是从JSON加载的题干文字"</p>`;
            html += `<p>实际: "${mainQuestion?.attributes?.content}"</p>`;
        }
        
        // 检查是否没有创建题干文字框
        if (!hasQuestionText) {
            html += '<p style="color: green;">✓ 从JSON加载时，没有创建不必要的题干文字框</p>';
        } else {
            html += '<p style="color: red;">✗ 从JSON加载时，错误地创建了题干文字框</p>';
        }
        
        // 检查JSON生成是否正确
        const regeneratedContent = generatedJSON?.["大题1"]?.["题干文字"];
        if (regeneratedContent === "这是从JSON加载的题干文字") {
            html += '<p style="color: green;">✓ 重新生成的JSON中题干文字正确</p>';
        } else {
            html += '<p style="color: red;">✗ 重新生成的JSON中题干文字不正确</p>';
            html += `<p>期望: "这是从JSON加载的题干文字"</p>`;
            html += `<p>实际: "${regeneratedContent}"</p>`;
        }
        
        // 模拟UI回显测试
        html += '<h3>UI回显逻辑测试</h3>';
        
        // 模拟UI管理器的回显逻辑
        function simulateUIDisplay(annotation) {
            // 模拟获取题干文字框（这里应该为空）
            const questionTexts = annotations.filter(ann =>
                ann.type === 'question-text' && ann.parentId === annotation.id
            );
            
            // 模拟UI回显逻辑
            const questionTextContent = questionTexts.length > 0 ? 
                (questionTexts[0].attributes.content || '') : 
                (annotation.attributes.content || '');
                
            return questionTextContent;
        }
        
        const displayedContent = simulateUIDisplay(mainQuestion);
        if (displayedContent === "这是从JSON加载的题干文字") {
            html += '<p style="color: green;">✓ UI回显逻辑正确：没有题干文字框时显示大题的content</p>';
            html += `<p>回显内容: "${displayedContent}"</p>`;
        } else {
            html += '<p style="color: red;">✗ UI回显逻辑错误</p>';
            html += `<p>期望: "这是从JSON加载的题干文字"</p>`;
            html += `<p>实际: "${displayedContent}"</p>`;
        }
        
        results.innerHTML = html;
        
        // 在控制台输出详细信息
        console.log("\n=== 详细测试结果 ===");
        console.log("原始JSON:", testJSON);
        console.log("解析后的标注:", annotations);
        console.log("重新生成的JSON:", generatedJSON);
        console.log("UI回显内容:", displayedContent);
    </script>
</body>
</html>
