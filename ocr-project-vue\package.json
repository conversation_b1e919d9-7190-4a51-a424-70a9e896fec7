{"name": "ocr-annotation-tool-vue", "version": "1.0.0", "description": "OCR标注工具 - Vue3 + Electron版本", "main": "dist-electron/main.js", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "electron:build": "npm run build && electron-builder", "electron:build-win": "npm run build && electron-builder --win", "electron:build-mac": "npm run build && electron-builder --mac", "electron:build-linux": "npm run build && electron-builder --linux", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "keywords": ["ocr", "annotation", "vue3", "electron", "typescript", "image-processing", "machine-learning"], "author": "OCR Annotation Tool Team", "license": "MIT", "dependencies": {"vue": "^3.4.0", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "fabric": "^5.3.0", "lodash-es": "^4.17.21", "dayjs": "^1.11.10"}, "devDependencies": {"@types/node": "^20.10.0", "@types/lodash-es": "^4.17.12", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-typescript": "^12.0.0", "concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.9.1", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.19.2", "sass": "^1.69.5", "typescript": "~5.3.0", "vite": "^5.0.8", "vite-plugin-electron": "^0.15.5", "vite-plugin-electron-renderer": "^0.14.5", "vue-tsc": "^1.8.25", "wait-on": "^7.2.0"}, "build": {"appId": "com.ocrtools.annotation.vue", "productName": "OCR标注工具", "directories": {"output": "dist-app"}, "files": ["dist/**/*", "dist-electron/**/*", "package.json"], "extraResources": [{"from": "resources", "to": "resources", "filter": ["**/*"]}], "win": {"target": "nsis", "icon": "build/icon.ico"}, "mac": {"target": "dmg", "icon": "build/icon.icns"}, "linux": {"target": "AppImage", "icon": "build/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}