<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR识别保存测试</title>
</head>
<body>
    <h1>OCR识别保存测试</h1>
    
    <div id="test-results"></div>
    
    <script src="js/utils.js"></script>
    <script src="js/data-manager.js"></script>
    
    <script>
        // 模拟OCR识别完成后的保存流程
        
        // 1. 创建模拟标注数据（包含题干文字框）
        const mockAnnotations = [
            {
                id: "main_1",
                type: "main-question",
                number: 1,
                coordinates: [[55, 750], [786, 1139]],
                attributes: {
                    content: "",
                    questionType: "填空题",
                    hasImage: false
                }
            },
            {
                id: "questiontext_1",
                type: "question-text",
                number: null,
                coordinates: [[60, 755], [780, 800]],
                parentId: "main_1",
                attributes: {
                    content: "", // OCR识别前为空
                    printWriteAttribute: "印刷"
                }
            }
        ];
        
        // 2. 模拟OCR识别结果
        const ocrResults = [
            {
                annotationId: "questiontext_1",
                type: "question-text",
                success: true,
                ocrText: "这是OCR识别出的题干文字内容"
            }
        ];
        
        // 3. 模拟OCR处理完成后的更新逻辑
        function simulateOCRUpdate(annotations, results) {
            let updatedCount = 0;
            
            results.forEach(result => {
                if (result.success && result.ocrText) {
                    const annotation = annotations.find(ann => ann.id === result.annotationId);
                    if (annotation) {
                        // 根据标注类型更新相应字段
                        switch (result.type) {
                            case 'main-question':
                            case 'question-text':
                            case 'sub-question':
                                annotation.attributes.content = result.ocrText;
                                break;
                            case 'answer-area':
                                annotation.attributes.answerContent = result.ocrText;
                                break;
                            case 'image-area':
                                annotation.attributes.imageDescription = result.ocrText;
                                break;
                        }
                        updatedCount++;
                    }
                }
            });
            
            return updatedCount;
        }
        
        // 4. 执行测试
        console.log("OCR识别前的标注数据:", JSON.parse(JSON.stringify(mockAnnotations)));
        
        const updatedCount = simulateOCRUpdate(mockAnnotations, ocrResults);
        
        console.log("OCR识别后的标注数据:", JSON.parse(JSON.stringify(mockAnnotations)));
        
        // 5. 测试JSON生成
        const dataManager = new DataManager();
        const generatedJSON = dataManager.generateJSONFromAnnotations(
            { name: "test.jpg" },
            mockAnnotations,
            {}
        );
        
        console.log("生成的JSON:", generatedJSON);
        
        // 6. 验证结果
        const results = document.getElementById('test-results');
        let html = '<h2>OCR识别保存测试结果</h2>';
        
        // 检查题干文字框是否正确更新
        const questionTextAnnotation = mockAnnotations.find(ann => ann.type === 'question-text');
        const expectedContent = "这是OCR识别出的题干文字内容";
        
        if (questionTextAnnotation && questionTextAnnotation.attributes.content === expectedContent) {
            html += '<p style="color: green;">✓ OCR识别结果正确保存到题干文字框</p>';
            html += `<p>题干文字框内容: "${questionTextAnnotation.attributes.content}"</p>`;
        } else {
            html += '<p style="color: red;">✗ OCR识别结果未正确保存到题干文字框</p>';
            html += `<p>期望: "${expectedContent}"</p>`;
            html += `<p>实际: "${questionTextAnnotation?.attributes?.content}"</p>`;
        }
        
        // 检查JSON生成是否正确
        const jsonContent = generatedJSON?.["大题1"]?.["题干文字"];
        if (jsonContent === expectedContent) {
            html += '<p style="color: green;">✓ JSON中的题干文字正确来自OCR识别结果</p>';
            html += `<p>JSON题干文字: "${jsonContent}"</p>`;
        } else {
            html += '<p style="color: red;">✗ JSON中的题干文字不正确</p>';
            html += `<p>期望: "${expectedContent}"</p>`;
            html += `<p>实际: "${jsonContent}"</p>`;
        }
        
        // 检查更新计数
        if (updatedCount === 1) {
            html += '<p style="color: green;">✓ 正确更新了1个标注</p>';
        } else {
            html += '<p style="color: red;">✗ 更新计数不正确</p>';
            html += `<p>期望: 1, 实际: ${updatedCount}</p>`;
        }
        
        html += '<h3>数据流向验证</h3>';
        html += '<p>1. OCR识别 → 题干文字框.attributes.content</p>';
        html += '<p>2. 题干文字框.attributes.content → JSON."题干文字"字段</p>';
        html += '<p>3. 需要调用 saveCurrentAnnotations() 保存到内存</p>';
        
        results.innerHTML = html;
        
        // 在控制台输出详细信息
        console.log("\n=== OCR保存测试详细结果 ===");
        console.log("更新计数:", updatedCount);
        console.log("题干文字框内容:", questionTextAnnotation?.attributes?.content);
        console.log("生成的JSON:", generatedJSON);
    </script>
</body>
</html>
