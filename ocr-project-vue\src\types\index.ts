// 基础类型定义
export interface Point {
  x: number
  y: number
}

export interface Size {
  width: number
  height: number
}

export interface Rect {
  x: number
  y: number
  width: number
  height: number
}

// 标注类型枚举
export enum AnnotationType {
  MAIN_QUESTION = 'main-question',
  SUB_QUESTION = 'sub-question',
  ANSWER_AREA = 'answer-area',
  IMAGE_AREA = 'image-area',
  QUESTION_TEXT = 'question-text'
}

// 标注属性接口
export interface AnnotationAttributes {
  content?: string
  number?: number
  score?: number
  difficulty?: string
  type?: string
  [key: string]: any
}

// 标注接口
export interface Annotation {
  id: string
  type: AnnotationType
  parentId?: string
  coordinates: Point[]
  attributes: AnnotationAttributes
  createdAt: Date
  updatedAt: Date
}

// 图片信息接口
export interface ImageInfo {
  id: string
  name: string
  path: string
  size: Size
  annotations: Annotation[]
  createdAt: Date
  updatedAt: Date
}

// 工作区接口
export interface Workspace {
  id: string
  name: string
  path: string
  images: ImageInfo[]
  settings: WorkspaceSettings
  createdAt: Date
  updatedAt: Date
}

// 工作区设置接口
export interface WorkspaceSettings {
  autoSave: boolean
  saveInterval: number
  ocrProvider: string
  ocrSettings: OCRSettings
  qualityCheck: boolean
  [key: string]: any
}

// OCR设置接口
export interface OCRSettings {
  provider: 'doubao' | 'qwen' | 'custom'
  apiKey?: string
  apiUrl?: string
  useTrueBatch: boolean
  batchSize: number
  timeout: number
  [key: string]: any
}

// OCR结果接口
export interface OCRResult {
  text: string
  confidence: number
  boundingBox?: Rect
}

// 应用状态接口
export interface AppState {
  currentWorkspace?: Workspace
  currentImage?: ImageInfo
  selectedAnnotations: string[]
  tool: string
  mode: 'annotation' | 'quality-check'
  loading: boolean
  error?: string
}

// 画布状态接口
export interface CanvasState {
  scale: number
  offset: Point
  size: Size
  imageSize: Size
  isDragging: boolean
  isDrawing: boolean
  showGrid: boolean
  showRuler: boolean
}

// 工具类型
export type Tool = 
  | 'select'
  | 'main-question'
  | 'sub-question'
  | 'answer-area'
  | 'image-area'
  | 'question-text'
  | 'pan'
  | 'zoom'

// 菜单动作类型
export type MenuAction = 
  | 'open-images'
  | 'open-folder'
  | 'save'
  | 'export-json'

// 文件类型
export interface FileInfo {
  name: string
  path: string
  size: number
  type: string
  lastModified: Date
}

// 导出选项
export interface ExportOptions {
  format: 'json' | 'excel' | 'csv'
  includeImages: boolean
  includeAnnotations: boolean
  outputPath: string
}

// 质检结果
export interface QualityCheckResult {
  imageId: string
  issues: QualityIssue[]
  score: number
  status: 'pass' | 'warning' | 'error'
}

export interface QualityIssue {
  type: 'missing-annotation' | 'invalid-content' | 'coordinate-error' | 'duplicate-annotation'
  severity: 'low' | 'medium' | 'high'
  message: string
  annotationId?: string
}

// 快捷键配置
export interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  shiftKey?: boolean
  altKey?: boolean
  action: string
  description: string
}

// 设置接口
export interface Settings {
  general: {
    language: string
    theme: 'light' | 'dark' | 'auto'
    autoSave: boolean
    saveInterval: number
  }
  canvas: {
    showGrid: boolean
    showRuler: boolean
    gridSize: number
    snapToGrid: boolean
  }
  annotation: {
    defaultColors: Record<AnnotationType, string>
    strokeWidth: number
    fontSize: number
    showLabels: boolean
  }
  ocr: OCRSettings
  shortcuts: KeyboardShortcut[]
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// 事件类型
export interface AnnotationEvent {
  type: 'create' | 'update' | 'delete' | 'select'
  annotation: Annotation
  oldAnnotation?: Annotation
}

export interface ImageEvent {
  type: 'load' | 'change' | 'zoom' | 'pan'
  image?: ImageInfo
  scale?: number
  offset?: Point
}

// 组件Props类型
export interface ToolbarProps {
  currentTool: Tool
  onToolChange: (tool: Tool) => void
  disabled?: boolean
}

export interface CanvasProps {
  image?: ImageInfo
  annotations: Annotation[]
  selectedIds: string[]
  tool: Tool
  onAnnotationCreate: (annotation: Omit<Annotation, 'id' | 'createdAt' | 'updatedAt'>) => void
  onAnnotationUpdate: (id: string, updates: Partial<Annotation>) => void
  onAnnotationDelete: (id: string) => void
  onSelectionChange: (ids: string[]) => void
}

export interface PanelProps {
  annotations: Annotation[]
  selectedIds: string[]
  onAnnotationUpdate: (id: string, updates: Partial<Annotation>) => void
  onAnnotationDelete: (id: string) => void
  onSelectionChange: (ids: string[]) => void
}
