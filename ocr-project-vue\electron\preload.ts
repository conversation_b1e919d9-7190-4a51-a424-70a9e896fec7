import { contextBridge, ipcRenderer } from 'electron'

// 暴露受保护的方法，允许渲染进程使用
contextBridge.exposeInMainWorld('electronAPI', {
  // 对话框
  dialog: {
    openFile: (options: Electron.OpenDialogOptions) => ipcRenderer.invoke('dialog:openFile', options),
    saveFile: (options: Electron.SaveDialogOptions) => ipcRenderer.invoke('dialog:saveFile', options)
  },

  // 文件系统
  fs: {
    readFile: (filePath: string) => ipcRenderer.invoke('fs:readFile', filePath),
    writeFile: (filePath: string, data: string) => ipcRenderer.invoke('fs:writeFile', filePath, data),
    readdir: (dirPath: string) => ipcRenderer.invoke('fs:readdir', dirPath),
    stat: (filePath: string) => ipcRenderer.invoke('fs:stat', filePath),
    mkdir: (dirPath: string) => ipcRenderer.invoke('fs:mkdir', dirPath),
    exists: (filePath: string) => ipcRenderer.invoke('fs:exists', filePath)
  },

  // 菜单事件监听
  onMenuAction: (callback: (action: string) => void) => {
    ipcRenderer.on('menu-open-images', () => callback('open-images'))
    ipcRenderer.on('menu-open-folder', () => callback('open-folder'))
    ipcRenderer.on('menu-save', () => callback('save'))
    ipcRenderer.on('menu-export-json', () => callback('export-json'))
  },

  // 移除监听器
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel)
  }
})

// 类型声明
declare global {
  interface Window {
    electronAPI: {
      dialog: {
        openFile: (options: Electron.OpenDialogOptions) => Promise<Electron.OpenDialogReturnValue>
        saveFile: (options: Electron.SaveDialogOptions) => Promise<Electron.SaveDialogReturnValue>
      }
      fs: {
        readFile: (filePath: string) => Promise<{ success: boolean; data?: string; error?: string }>
        writeFile: (filePath: string, data: string) => Promise<{ success: boolean; error?: string }>
        readdir: (dirPath: string) => Promise<{ success: boolean; data?: string[]; error?: string }>
        stat: (filePath: string) => Promise<{ success: boolean; data?: any; error?: string }>
        mkdir: (dirPath: string) => Promise<{ success: boolean; error?: string }>
        exists: (filePath: string) => Promise<boolean>
      }
      onMenuAction: (callback: (action: string) => void) => void
      removeAllListeners: (channel: string) => void
    }
  }
}
