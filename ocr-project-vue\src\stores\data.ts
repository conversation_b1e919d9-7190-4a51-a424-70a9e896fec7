import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import type { Workspace, ImageInfo, Annotation, ExportOptions } from '@/types'
import { generateId, safeJsonParse, safeJsonStringify, formatDateTime } from '@/utils/common'
import { useImageStore } from './image'
import { useAnnotationStore } from './annotation'
import { useAppStore } from './app'

export const useDataStore = defineStore('data', () => {
  // 状态
  const currentWorkspace = ref<Workspace>()
  const workspaces = ref<Workspace[]>([])
  const autoSaveTimer = ref<NodeJS.Timeout>()
  const lastSaveTime = ref<Date>()
  const hasUnsavedChanges = ref(false)

  // 获取其他store
  const imageStore = useImageStore()
  const annotationStore = useAnnotationStore()
  const appStore = useAppStore()

  // 计算属性
  const hasWorkspace = computed(() => !!currentWorkspace.value)
  
  const workspaceName = computed(() => currentWorkspace.value?.name || '')
  
  const workspacePath = computed(() => currentWorkspace.value?.path || '')
  
  const imageCount = computed(() => currentWorkspace.value?.images.length || 0)
  
  const annotationCount = computed(() => {
    if (!currentWorkspace.value) return 0
    return currentWorkspace.value.images.reduce((total, img) => total + img.annotations.length, 0)
  })

  // 方法
  const createWorkspace = async (name: string, path?: string): Promise<Workspace> => {
    const workspace: Workspace = {
      id: generateId(),
      name,
      path: path || '',
      images: [],
      settings: {
        autoSave: true,
        saveInterval: 30000,
        ocrProvider: 'doubao',
        ocrSettings: {
          provider: 'doubao',
          useTrueBatch: false,
          batchSize: 10,
          timeout: 30000
        },
        qualityCheck: true
      },
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    workspaces.value.push(workspace)
    await saveWorkspaceList()
    
    return workspace
  }

  const openWorkspace = async (workspace: Workspace) => {
    currentWorkspace.value = workspace
    
    // 加载工作区的图片
    imageStore.clearImages()
    if (workspace.images.length > 0) {
      imageStore.addImages(workspace.images)
      imageStore.setCurrentImage(0)
      
      // 加载第一张图片的标注
      if (workspace.images[0].annotations.length > 0) {
        annotationStore.loadAnnotations(workspace.images[0].annotations)
      }
    }
    
    // 启动自动保存
    startAutoSave()
  }

  const closeWorkspace = async () => {
    if (hasUnsavedChanges.value) {
      await saveCurrentData()
    }
    
    stopAutoSave()
    currentWorkspace.value = undefined
    imageStore.clearImages()
    annotationStore.clearAnnotations()
  }

  const saveWorkspace = async (workspace: Workspace) => {
    if (!window.electronAPI) {
      throw new Error('Electron API not available')
    }

    const workspaceData = {
      ...workspace,
      updatedAt: new Date()
    }

    const jsonData = safeJsonStringify(workspaceData, 2)
    const workspacePath = workspace.path || `${workspace.name}.workspace.json`
    
    const result = await window.electronAPI.fs.writeFile(workspacePath, jsonData)
    if (!result.success) {
      throw new Error(result.error || 'Failed to save workspace')
    }
    
    lastSaveTime.value = new Date()
    hasUnsavedChanges.value = false
  }

  const loadWorkspace = async (filePath: string): Promise<Workspace> => {
    if (!window.electronAPI) {
      throw new Error('Electron API not available')
    }

    const result = await window.electronAPI.fs.readFile(filePath)
    if (!result.success || !result.data) {
      throw new Error(result.error || 'Failed to load workspace')
    }

    const workspaceData = safeJsonParse<Workspace>(result.data, {} as Workspace)
    if (!workspaceData.id) {
      throw new Error('Invalid workspace file')
    }

    // 更新工作区路径
    workspaceData.path = filePath
    
    // 验证图片文件是否存在
    for (const image of workspaceData.images) {
      const exists = await window.electronAPI.fs.exists(image.path)
      if (!exists) {
        console.warn(`Image file not found: ${image.path}`)
      }
    }

    return workspaceData
  }

  const saveCurrentData = async () => {
    if (!currentWorkspace.value) return

    // 更新当前图片的标注
    const currentImage = imageStore.currentImage
    if (currentImage) {
      currentImage.annotations = [...annotationStore.annotations]
      currentImage.updatedAt = new Date()
    }

    // 更新工作区的图片列表
    currentWorkspace.value.images = [...imageStore.images]
    currentWorkspace.value.updatedAt = new Date()

    await saveWorkspace(currentWorkspace.value)
  }

  const exportJSON = async (options?: Partial<ExportOptions>) => {
    if (!currentWorkspace.value) {
      throw new Error('No workspace to export')
    }

    if (!window.electronAPI) {
      throw new Error('Electron API not available')
    }

    const defaultOptions: ExportOptions = {
      format: 'json',
      includeImages: true,
      includeAnnotations: true,
      outputPath: ''
    }

    const exportOptions = { ...defaultOptions, ...options }

    // 选择保存路径
    if (!exportOptions.outputPath) {
      const result = await window.electronAPI.dialog.saveFile({
        title: '导出JSON数据',
        defaultPath: `${currentWorkspace.value.name}_export_${formatDateTime(new Date()).replace(/[:\s]/g, '_')}.json`,
        filters: [
          { name: 'JSON文件', extensions: ['json'] },
          { name: '所有文件', extensions: ['*'] }
        ]
      })

      if (result.canceled || !result.filePath) {
        return
      }

      exportOptions.outputPath = result.filePath
    }

    // 生成导出数据
    const exportData = generateExportData(exportOptions)
    const jsonData = safeJsonStringify(exportData, 2)

    // 保存文件
    const writeResult = await window.electronAPI.fs.writeFile(exportOptions.outputPath, jsonData)
    if (!writeResult.success) {
      throw new Error(writeResult.error || 'Failed to export data')
    }

    return exportOptions.outputPath
  }

  const importJSON = async () => {
    if (!window.electronAPI) {
      throw new Error('Electron API not available')
    }

    const result = await window.electronAPI.dialog.openFile({
      title: '导入JSON数据',
      filters: [
        { name: 'JSON文件', extensions: ['json'] },
        { name: '所有文件', extensions: ['*'] }
      ],
      properties: ['openFile']
    })

    if (result.canceled || !result.filePaths.length) {
      return
    }

    const filePath = result.filePaths[0]
    const readResult = await window.electronAPI.fs.readFile(filePath)
    
    if (!readResult.success || !readResult.data) {
      throw new Error(readResult.error || 'Failed to read file')
    }

    const importData = safeJsonParse(readResult.data, null)
    if (!importData) {
      throw new Error('Invalid JSON format')
    }

    // 处理导入的数据
    await processImportData(importData)
  }

  const generateExportData = (options: ExportOptions) => {
    if (!currentWorkspace.value) return {}

    const data: any = {
      workspace: {
        id: currentWorkspace.value.id,
        name: currentWorkspace.value.name,
        exportedAt: new Date(),
        settings: currentWorkspace.value.settings
      }
    }

    if (options.includeImages) {
      data.images = currentWorkspace.value.images.map(image => ({
        id: image.id,
        name: image.name,
        path: image.path,
        size: image.size,
        createdAt: image.createdAt,
        updatedAt: image.updatedAt,
        annotations: options.includeAnnotations ? image.annotations : []
      }))
    }

    if (options.includeAnnotations && !options.includeImages) {
      // 只导出标注数据
      data.annotations = currentWorkspace.value.images.reduce((all: Annotation[], image) => {
        return all.concat(image.annotations.map(ann => ({
          ...ann,
          imageId: image.id,
          imageName: image.name
        })))
      }, [])
    }

    return data
  }

  const processImportData = async (data: any) => {
    // 简化实现：假设导入的是完整的工作区数据
    if (data.workspace && data.images) {
      const workspace: Workspace = {
        id: data.workspace.id || generateId(),
        name: data.workspace.name || 'Imported Workspace',
        path: '',
        images: data.images.map((img: any) => ({
          id: img.id || generateId(),
          name: img.name,
          path: img.path,
          size: img.size || { width: 0, height: 0 },
          annotations: img.annotations || [],
          createdAt: new Date(img.createdAt || Date.now()),
          updatedAt: new Date(img.updatedAt || Date.now())
        })),
        settings: data.workspace.settings || {
          autoSave: true,
          saveInterval: 30000,
          ocrProvider: 'doubao',
          ocrSettings: {
            provider: 'doubao',
            useTrueBatch: false,
            batchSize: 10,
            timeout: 30000
          },
          qualityCheck: true
        },
        createdAt: new Date(data.workspace.createdAt || Date.now()),
        updatedAt: new Date()
      }

      await openWorkspace(workspace)
    }
  }

  const startAutoSave = () => {
    stopAutoSave()
    
    if (appStore.autoSaveEnabled) {
      const interval = appStore.settings.general.saveInterval
      autoSaveTimer.value = setInterval(async () => {
        if (hasUnsavedChanges.value) {
          try {
            await saveCurrentData()
          } catch (error) {
            console.error('Auto save failed:', error)
          }
        }
      }, interval)
    }
  }

  const stopAutoSave = () => {
    if (autoSaveTimer.value) {
      clearInterval(autoSaveTimer.value)
      autoSaveTimer.value = undefined
    }
  }

  const markAsChanged = () => {
    hasUnsavedChanges.value = true
  }

  const saveWorkspaceList = async () => {
    try {
      if (window.electronAPI) {
        const workspaceListPath = 'workspaces.json'
        const data = safeJsonStringify(workspaces.value, 2)
        await window.electronAPI.fs.writeFile(workspaceListPath, data)
      } else {
        localStorage.setItem('ocr-tool-workspaces', safeJsonStringify(workspaces.value))
      }
    } catch (error) {
      console.error('Failed to save workspace list:', error)
    }
  }

  const loadWorkspaceList = async () => {
    try {
      if (window.electronAPI) {
        const workspaceListPath = 'workspaces.json'
        const exists = await window.electronAPI.fs.exists(workspaceListPath)
        if (exists) {
          const result = await window.electronAPI.fs.readFile(workspaceListPath)
          if (result.success && result.data) {
            workspaces.value = safeJsonParse(result.data, [])
          }
        }
      } else {
        const saved = localStorage.getItem('ocr-tool-workspaces')
        if (saved) {
          workspaces.value = safeJsonParse(saved, [])
        }
      }
    } catch (error) {
      console.error('Failed to load workspace list:', error)
    }
  }

  const initialize = async () => {
    await loadWorkspaceList()
  }

  return {
    // 状态
    currentWorkspace: readonly(currentWorkspace),
    workspaces: readonly(workspaces),
    lastSaveTime: readonly(lastSaveTime),
    hasUnsavedChanges: readonly(hasUnsavedChanges),
    
    // 计算属性
    hasWorkspace,
    workspaceName,
    workspacePath,
    imageCount,
    annotationCount,
    
    // 方法
    createWorkspace,
    openWorkspace,
    closeWorkspace,
    saveWorkspace,
    loadWorkspace,
    saveCurrentData,
    exportJSON,
    importJSON,
    markAsChanged,
    initialize
  }
})
