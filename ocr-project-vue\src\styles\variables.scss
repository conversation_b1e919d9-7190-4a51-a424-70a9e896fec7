// 颜色变量
:root {
  // 主色调
  --primary-color: #2c3e50;
  --secondary-color: #3498db;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --info-color: #17a2b8;
  
  // 背景色
  --bg-color: #f5f5f5;
  --bg-secondary: #ffffff;
  --bg-dark: #2c3e50;
  
  // 文字颜色
  --text-color: #333333;
  --text-secondary: #666666;
  --text-light: #999999;
  --text-white: #ffffff;
  
  // 边框颜色
  --border-color: #e0e0e0;
  --border-light: #f0f0f0;
  --border-dark: #cccccc;
  
  // 标注颜色
  --annotation-main-question: #e74c3c;
  --annotation-sub-question: #2ecc71;
  --annotation-answer-area: #9b59b6;
  --annotation-image-area: #f39c12;
  --annotation-question-text: #3498db;
  
  // 阴影
  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.2);
  
  // 圆角
  --border-radius-small: 4px;
  --border-radius-medium: 6px;
  --border-radius-large: 8px;
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-xxl: 24px;
  
  // 字体大小
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;
  
  // 布局尺寸
  --header-height: 60px;
  --sidebar-width: 300px;
  --panel-width: 350px;
  --status-bar-height: 30px;
  
  // 过渡动画
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

// SCSS变量（用于计算）
$primary-color: #2c3e50;
$secondary-color: #3498db;
$success-color: #27ae60;
$warning-color: #f39c12;
$danger-color: #e74c3c;

$bg-color: #f5f5f5;
$bg-secondary: #ffffff;

$text-color: #333333;
$text-secondary: #666666;
$text-light: #999999;

$border-color: #e0e0e0;

$header-height: 60px;
$sidebar-width: 300px;
$panel-width: 350px;
$status-bar-height: 30px;

// 混合器
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

@mixin button-base {
  padding: 8px 16px;
  border: none;
  border-radius: var(--border-radius-medium);
  cursor: pointer;
  transition: var(--transition-fast);
  font-size: var(--font-size-sm);
  
  &:hover {
    opacity: 0.8;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

@mixin card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-medium);
  box-shadow: var(--shadow-light);
}

@mixin scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--bg-color);
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--border-dark);
    border-radius: 3px;
    
    &:hover {
      background: var(--text-secondary);
    }
  }
}
