<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR识别完整保存流程测试</title>
</head>
<body>
    <h1>OCR识别完整保存流程测试</h1>
    
    <div id="test-results"></div>
    
    <script>
        // 模拟OCR识别完成后的完整保存流程
        
        const results = document.getElementById('test-results');
        let html = '<h2>OCR识别完整保存流程分析</h2>';
        
        html += '<h3>当前保存流程</h3>';
        html += '<ol>';
        html += '<li><strong>OCR识别完成</strong>：更新标注的 attributes.content</li>';
        html += '<li><strong>保存到内存</strong>：调用 saveCurrentAnnotations()</li>';
        html += '<li><strong>保存到本地</strong>：调用 autoSaveCurrentImage()</li>';
        html += '<li><strong>刷新UI</strong>：更新界面显示</li>';
        html += '</ol>';
        
        html += '<h3>保存机制说明</h3>';
        html += '<div style="background: #f5f5f5; padding: 10px; margin: 10px 0;">';
        html += '<h4>1. saveCurrentAnnotations() - 内存保存</h4>';
        html += '<p>• 将当前图片的所有标注数据保存到内存中的 annotationData Map</p>';
        html += '<p>• 用于快速切换图片时的数据恢复</p>';
        html += '<p>• 不会丢失，但页面刷新后会清空</p>';
        html += '</div>';
        
        html += '<div style="background: #e8f5e8; padding: 10px; margin: 10px 0;">';
        html += '<h4>2. autoSaveCurrentImage() - 本地文件保存</h4>';
        html += '<p>• 将标注数据转换为JSON格式</p>';
        html += '<p>• 按大题分别保存到工作区的JSON文件夹</p>';
        html += '<p>• 文件名格式：{图片名}大题{编号}.json</p>';
        html += '<p>• 持久化保存，页面刷新后仍然存在</p>';
        html += '</div>';
        
        html += '<h3>OCR识别后的数据流向</h3>';
        html += '<div style="background: #fff3cd; padding: 10px; margin: 10px 0;">';
        html += '<p><strong>步骤1：</strong> OCR识别 → 题干文字框.attributes.content</p>';
        html += '<p><strong>步骤2：</strong> saveCurrentAnnotations() → 内存中的 annotationData</p>';
        html += '<p><strong>步骤3：</strong> autoSaveCurrentImage() → 工作区JSON文件</p>';
        html += '<p><strong>步骤4：</strong> 用户可以看到识别结果并且数据已持久化</p>';
        html += '</div>';
        
        html += '<h3>验证方法</h3>';
        html += '<ol>';
        html += '<li><strong>创建题干文字框</strong>并进行OCR识别</li>';
        html += '<li><strong>检查控制台</strong>是否显示"保存图片 XXX 的标注数据到内存"</li>';
        html += '<li><strong>检查控制台</strong>是否显示"自动保存完成: XXX, 保存N个大题"</li>';
        html += '<li><strong>检查工作区JSON文件夹</strong>是否生成了对应的JSON文件</li>';
        html += '<li><strong>打开JSON文件</strong>检查"题干文字"字段是否包含OCR识别的内容</li>';
        html += '<li><strong>刷新页面</strong>重新加载，检查数据是否正确恢复</li>';
        html += '</ol>';
        
        html += '<h3>可能的问题和解决方案</h3>';
        html += '<div style="background: #f8d7da; padding: 10px; margin: 10px 0;">';
        html += '<h4>问题1：没有工作区</h4>';
        html += '<p>• 如果没有设置工作区，autoSaveCurrentImage() 会直接返回</p>';
        html += '<p>• 解决：确保已经通过"设置工作区"按钮选择了工作区文件夹</p>';
        html += '</div>';
        
        html += '<div style="background: #f8d7da; padding: 10px; margin: 10px 0;">';
        html += '<h4>问题2：权限问题</h4>';
        html += '<p>• 浏览器可能没有写入文件的权限</p>';
        html += '<p>• 解决：检查浏览器控制台是否有权限相关的错误信息</p>';
        html += '</div>';
        
        html += '<div style="background: #f8d7da; padding: 10px; margin: 10px 0;">';
        html += '<h4>问题3：异步执行问题</h4>';
        html += '<p>• autoSaveCurrentImage() 是异步方法，需要等待完成</p>';
        html += '<p>• 解决：使用 await 确保保存完成后再继续</p>';
        html += '</div>';
        
        html += '<h3>修改后的代码逻辑</h3>';
        html += '<pre style="background: #f8f9fa; padding: 10px; border-left: 4px solid #007bff;">';
        html += `// OCR处理完成后
Utils.showNotification(\`OCR处理完成，已更新 \${updatedCount} 个标注\`, 'success');

// 保存OCR识别结果到内存
this.saveCurrentAnnotations();

// 保存OCR识别结果到本地文件
await this.autoSaveCurrentImage();

// 刷新UI显示
this.updateAnnotationLists();`;
        html += '</pre>';
        
        html += '<div style="background: #d4edda; padding: 10px; margin: 10px 0;">';
        html += '<h4>✅ 修改完成</h4>';
        html += '<p>现在OCR识别完成后会同时保存到内存和本地文件，确保数据不会丢失。</p>';
        html += '</div>';
        
        results.innerHTML = html;
        
        console.log("=== OCR完整保存流程测试 ===");
        console.log("1. OCR识别 → 更新标注内容");
        console.log("2. saveCurrentAnnotations() → 保存到内存");
        console.log("3. autoSaveCurrentImage() → 保存到本地文件");
        console.log("4. updateAnnotationLists() → 刷新UI");
        console.log("修改已完成，OCR识别的内容现在会正确保存到本地文件。");
    </script>
</body>
</html>
