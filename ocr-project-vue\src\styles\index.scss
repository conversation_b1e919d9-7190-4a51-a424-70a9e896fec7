@import './variables.scss';

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  font-size: var(--font-size-sm);
  line-height: 1.5;
  color: var(--text-color);
  background-color: var(--bg-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
}

// 通用样式
.btn {
  @include button-base;
  
  &.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-white);
  }
  
  &.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--text-white);
  }
  
  &.btn-success {
    background-color: var(--success-color);
    color: var(--text-white);
  }
  
  &.btn-warning {
    background-color: var(--warning-color);
    color: var(--text-white);
  }
  
  &.btn-danger {
    background-color: var(--danger-color);
    color: var(--text-white);
  }
  
  &.btn-outline {
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-color);
    
    &:hover {
      background-color: var(--bg-color);
    }
  }
  
  &.btn-small {
    padding: 4px 8px;
    font-size: var(--font-size-xs);
  }
  
  &.btn-large {
    padding: 12px 24px;
    font-size: var(--font-size-md);
  }
}

.card {
  @include card;
  padding: var(--spacing-lg);
}

.form-group {
  margin-bottom: var(--spacing-md);
  
  label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-color);
  }
  
  input, textarea, select {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-small);
    font-size: var(--font-size-sm);
    transition: var(--transition-fast);
    
    &:focus {
      outline: none;
      border-color: var(--secondary-color);
      box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    }
  }
  
  textarea {
    resize: vertical;
    min-height: 80px;
  }
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--text-secondary); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }

.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-danger { background-color: var(--danger-color); }

.d-flex { display: flex; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-none { display: none; }

.flex-center { @include flex-center; }
.flex-between { @include flex-between; }
.flex-start { @include flex-start; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-1 { flex: 1; }

.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }

.justify-center { justify-content: center; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }

// 间距工具类
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

// 滚动条样式
.scrollbar {
  @include scrollbar;
}

// 动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-normal);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform var(--transition-normal);
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

// 响应式
@media (max-width: 1200px) {
  :root {
    --sidebar-width: 250px;
    --panel-width: 300px;
  }
}

@media (max-width: 768px) {
  :root {
    --sidebar-width: 200px;
    --panel-width: 250px;
    --font-size-sm: 12px;
    --font-size-md: 14px;
  }
}
