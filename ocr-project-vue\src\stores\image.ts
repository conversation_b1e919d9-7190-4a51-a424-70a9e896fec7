import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import type { ImageInfo, Size, Point } from '@/types'
import { generateId } from '@/utils/common'

export const useImageStore = defineStore('image', () => {
  // 状态
  const images = ref<ImageInfo[]>([])
  const currentImageIndex = ref(-1)
  const imageCache = ref<Map<string, HTMLImageElement>>(new Map())
  
  // 画布状态
  const canvasScale = ref(1)
  const canvasOffset = ref<Point>({ x: 0, y: 0 })
  const canvasSize = ref<Size>({ width: 0, height: 0 })
  const isDragging = ref(false)
  const isDrawing = ref(false)

  // 计算属性
  const currentImage = computed(() => {
    return currentImageIndex.value >= 0 ? images.value[currentImageIndex.value] : undefined
  })

  const hasImages = computed(() => images.value.length > 0)
  
  const canNavigatePrev = computed(() => currentImageIndex.value > 0)
  
  const canNavigateNext = computed(() => currentImageIndex.value < images.value.length - 1)

  const imageCount = computed(() => images.value.length)

  const currentImageNumber = computed(() => currentImageIndex.value + 1)

  // 方法
  const addImages = (newImages: ImageInfo[]) => {
    images.value.push(...newImages)
  }

  const removeImage = (id: string) => {
    const index = images.value.findIndex(img => img.id === id)
    if (index >= 0) {
      images.value.splice(index, 1)
      imageCache.value.delete(id)
      
      // 调整当前图片索引
      if (currentImageIndex.value >= index) {
        if (currentImageIndex.value > 0) {
          currentImageIndex.value--
        } else if (images.value.length === 0) {
          currentImageIndex.value = -1
        }
      }
    }
  }

  const setCurrentImage = (index: number) => {
    if (index >= 0 && index < images.value.length) {
      currentImageIndex.value = index
    }
  }

  const setCurrentImageById = (id: string) => {
    const index = images.value.findIndex(img => img.id === id)
    if (index >= 0) {
      setCurrentImage(index)
    }
  }

  const navigatePrev = () => {
    if (canNavigatePrev.value) {
      currentImageIndex.value--
    }
  }

  const navigateNext = () => {
    if (canNavigateNext.value) {
      currentImageIndex.value++
    }
  }

  const navigateToFirst = () => {
    if (hasImages.value) {
      currentImageIndex.value = 0
    }
  }

  const navigateToLast = () => {
    if (hasImages.value) {
      currentImageIndex.value = images.value.length - 1
    }
  }

  const loadImage = async (imagePath: string): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve(img)
      img.onerror = () => reject(new Error(`Failed to load image: ${imagePath}`))
      img.src = `file://${imagePath}`
    })
  }

  const cacheImage = async (imageInfo: ImageInfo) => {
    if (!imageCache.value.has(imageInfo.id)) {
      try {
        const img = await loadImage(imageInfo.path)
        imageCache.value.set(imageInfo.id, img)
        
        // 更新图片尺寸信息
        const imageIndex = images.value.findIndex(i => i.id === imageInfo.id)
        if (imageIndex >= 0) {
          images.value[imageIndex].size = {
            width: img.naturalWidth,
            height: img.naturalHeight
          }
        }
      } catch (error) {
        console.error('Failed to cache image:', error)
        throw error
      }
    }
  }

  const getCachedImage = (id: string): HTMLImageElement | undefined => {
    return imageCache.value.get(id)
  }

  const clearImageCache = () => {
    imageCache.value.clear()
  }

  // 画布操作
  const setCanvasSize = (size: Size) => {
    canvasSize.value = size
  }

  const setCanvasScale = (scale: number) => {
    canvasScale.value = Math.max(0.1, Math.min(5, scale))
  }

  const setCanvasOffset = (offset: Point) => {
    canvasOffset.value = offset
  }

  const zoomIn = () => {
    setCanvasScale(canvasScale.value * 1.2)
  }

  const zoomOut = () => {
    setCanvasScale(canvasScale.value / 1.2)
  }

  const resetZoom = () => {
    setCanvasScale(1)
    setCanvasOffset({ x: 0, y: 0 })
  }

  const fitToCanvas = () => {
    if (!currentImage.value || !canvasSize.value.width || !canvasSize.value.height) return

    const imageAspect = currentImage.value.size.width / currentImage.value.size.height
    const canvasAspect = canvasSize.value.width / canvasSize.value.height

    let scale: number
    if (imageAspect > canvasAspect) {
      scale = canvasSize.value.width / currentImage.value.size.width
    } else {
      scale = canvasSize.value.height / currentImage.value.size.height
    }

    setCanvasScale(scale * 0.9) // 留一些边距
    setCanvasOffset({ x: 0, y: 0 })
  }

  const panCanvas = (deltaX: number, deltaY: number) => {
    setCanvasOffset({
      x: canvasOffset.value.x + deltaX,
      y: canvasOffset.value.y + deltaY
    })
  }

  const setDragging = (dragging: boolean) => {
    isDragging.value = dragging
  }

  const setDrawing = (drawing: boolean) => {
    isDrawing.value = drawing
  }

  // 坐标转换
  const screenToImage = (screenPoint: Point): Point => {
    return {
      x: (screenPoint.x - canvasOffset.value.x) / canvasScale.value,
      y: (screenPoint.y - canvasOffset.value.y) / canvasScale.value
    }
  }

  const imageToScreen = (imagePoint: Point): Point => {
    return {
      x: imagePoint.x * canvasScale.value + canvasOffset.value.x,
      y: imagePoint.y * canvasScale.value + canvasOffset.value.y
    }
  }

  // 文件操作
  const openImages = async () => {
    if (!window.electronAPI) {
      throw new Error('Electron API not available')
    }

    const result = await window.electronAPI.dialog.openFile({
      title: '选择图片文件',
      filters: [
        { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'bmp', 'gif', 'webp'] },
        { name: '所有文件', extensions: ['*'] }
      ],
      properties: ['openFile', 'multiSelections']
    })

    if (!result.canceled && result.filePaths.length > 0) {
      const newImages: ImageInfo[] = []
      
      for (const filePath of result.filePaths) {
        const fileName = filePath.split(/[/\\]/).pop() || ''
        const imageInfo: ImageInfo = {
          id: generateId(),
          name: fileName,
          path: filePath,
          size: { width: 0, height: 0 }, // 将在缓存时更新
          annotations: [],
          createdAt: new Date(),
          updatedAt: new Date()
        }
        
        newImages.push(imageInfo)
        await cacheImage(imageInfo)
      }
      
      addImages(newImages)
      
      if (currentImageIndex.value < 0 && newImages.length > 0) {
        setCurrentImage(0)
      }
    }
  }

  const openFolder = async () => {
    if (!window.electronAPI) {
      throw new Error('Electron API not available')
    }

    const result = await window.electronAPI.dialog.openFile({
      title: '选择图片文件夹',
      properties: ['openDirectory']
    })

    if (!result.canceled && result.filePaths.length > 0) {
      const folderPath = result.filePaths[0]
      const dirResult = await window.electronAPI.fs.readdir(folderPath)
      
      if (dirResult.success && dirResult.data) {
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp']
        const imageFiles = dirResult.data.filter(file => 
          imageExtensions.some(ext => file.toLowerCase().endsWith(ext))
        )
        
        const newImages: ImageInfo[] = []
        
        for (const fileName of imageFiles) {
          const filePath = `${folderPath}/${fileName}`
          const imageInfo: ImageInfo = {
            id: generateId(),
            name: fileName,
            path: filePath,
            size: { width: 0, height: 0 },
            annotations: [],
            createdAt: new Date(),
            updatedAt: new Date()
          }
          
          newImages.push(imageInfo)
          try {
            await cacheImage(imageInfo)
          } catch (error) {
            console.warn(`Failed to cache image ${fileName}:`, error)
          }
        }
        
        addImages(newImages)
        
        if (currentImageIndex.value < 0 && newImages.length > 0) {
          setCurrentImage(0)
        }
      }
    }
  }

  const clearImages = () => {
    images.value = []
    currentImageIndex.value = -1
    clearImageCache()
  }

  return {
    // 状态
    images: readonly(images),
    currentImageIndex: readonly(currentImageIndex),
    canvasScale: readonly(canvasScale),
    canvasOffset: readonly(canvasOffset),
    canvasSize: readonly(canvasSize),
    isDragging: readonly(isDragging),
    isDrawing: readonly(isDrawing),
    
    // 计算属性
    currentImage,
    hasImages,
    canNavigatePrev,
    canNavigateNext,
    imageCount,
    currentImageNumber,
    
    // 方法
    addImages,
    removeImage,
    setCurrentImage,
    setCurrentImageById,
    navigatePrev,
    navigateNext,
    navigateToFirst,
    navigateToLast,
    cacheImage,
    getCachedImage,
    clearImageCache,
    setCanvasSize,
    setCanvasScale,
    setCanvasOffset,
    zoomIn,
    zoomOut,
    resetZoom,
    fitToCanvas,
    panCanvas,
    setDragging,
    setDrawing,
    screenToImage,
    imageToScreen,
    openImages,
    openFolder,
    clearImages
  }
})
