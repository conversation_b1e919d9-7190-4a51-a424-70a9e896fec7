<template>
  <div class="image-canvas-container">
    <div class="canvas-wrapper" ref="canvasWrapper">
      <canvas
        ref="canvas"
        class="image-canvas"
        @mousedown="handleMouseDown"
        @mousemove="handleMouseMove"
        @mouseup="handleMouseUp"
        @wheel="handleWheel"
        @contextmenu.prevent
      />
      
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-overlay">
        <el-loading-directive />
        <p>加载图片中...</p>
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="!currentImage" class="empty-state">
        <el-icon><Picture /></el-icon>
        <h3>请选择图片</h3>
        <p>从左侧面板选择图片或点击下方按钮添加图片</p>
        <el-button @click="openImages" type="primary">
          <el-icon><FolderOpened /></el-icon>
          添加图片
        </el-button>
      </div>
    </div>
    
    <!-- 画布工具栏 -->
    <div class="canvas-toolbar" v-if="currentImage">
      <div class="toolbar-left">
        <el-button-group size="small">
          <el-button @click="fitToCanvas" title="适应画布">
            <el-icon><FullScreen /></el-icon>
          </el-button>
          <el-button @click="resetZoom" title="实际大小">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </el-button-group>
      </div>
      
      <div class="toolbar-center">
        <span class="zoom-level">{{ Math.round(canvasScale * 100) }}%</span>
      </div>
      
      <div class="toolbar-right">
        <el-button-group size="small">
          <el-button 
            :type="showGrid ? 'primary' : 'default'"
            @click="toggleGrid"
            title="显示网格"
          >
            <el-icon><Grid /></el-icon>
          </el-button>
          <el-button 
            :type="showRuler ? 'primary' : 'default'"
            @click="toggleRuler"
            title="显示标尺"
          >
            <el-icon><Ruler /></el-icon>
          </el-button>
        </el-button-group>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { 
  Picture, FolderOpened, FullScreen, Refresh, Grid
} from '@element-plus/icons-vue'
import { useAppStore } from '@/stores/app'
import { useImageStore } from '@/stores/image'
import { useAnnotationStore } from '@/stores/annotation'
import { CanvasRenderer } from '@/utils/canvas'
import type { Point } from '@/types'

// Store
const appStore = useAppStore()
const imageStore = useImageStore()
const annotationStore = useAnnotationStore()

// 响应式数据
const canvas = ref<HTMLCanvasElement>()
const canvasWrapper = ref<HTMLDivElement>()
const canvasRenderer = ref<CanvasRenderer>()
const isLoading = ref(false)
const isDragging = ref(false)
const isDrawing = ref(false)
const lastMousePos = ref<Point>({ x: 0, y: 0 })
const drawingPoints = ref<Point[]>([])

// 计算属性
const currentImage = computed(() => imageStore.currentImage)
const canvasScale = computed(() => imageStore.canvasScale)
const canvasOffset = computed(() => imageStore.canvasOffset)
const currentTool = computed(() => appStore.currentTool)
const annotations = computed(() => annotationStore.annotations)
const selectedIds = computed(() => annotationStore.selectedIds)

const showGrid = computed({
  get: () => appStore.settings.canvas.showGrid,
  set: (value) => appStore.updateSettings({ canvas: { ...appStore.settings.canvas, showGrid: value } })
})

const showRuler = computed({
  get: () => appStore.settings.canvas.showRuler,
  set: (value) => appStore.updateSettings({ canvas: { ...appStore.settings.canvas, showRuler: value } })
})

// 方法
const initCanvas = async () => {
  if (!canvas.value || !canvasWrapper.value) return

  const rect = canvasWrapper.value.getBoundingClientRect()
  canvas.value.width = rect.width
  canvas.value.height = rect.height

  imageStore.setCanvasSize({ width: rect.width, height: rect.height })

  canvasRenderer.value = new CanvasRenderer(canvas.value)
  
  await renderCanvas()
}

const renderCanvas = async () => {
  if (!canvasRenderer.value || !canvas.value) return

  const ctx = canvas.value.getContext('2d')
  if (!ctx) return

  // 清空画布
  ctx.clearRect(0, 0, canvas.value.width, canvas.value.height)

  // 绘制背景
  ctx.fillStyle = '#f5f5f5'
  ctx.fillRect(0, 0, canvas.value.width, canvas.value.height)

  // 绘制网格
  if (showGrid.value) {
    canvasRenderer.value.drawGrid(canvasScale.value, canvasOffset.value)
  }

  // 绘制图片
  if (currentImage.value) {
    const cachedImage = imageStore.getCachedImage(currentImage.value.id)
    if (cachedImage) {
      canvasRenderer.value.drawImage(
        cachedImage,
        canvasScale.value,
        canvasOffset.value
      )
    }
  }

  // 绘制标注
  if (annotations.value.length > 0) {
    canvasRenderer.value.drawAnnotations(
      annotations.value,
      selectedIds.value,
      canvasScale.value,
      canvasOffset.value,
      appStore.settings.annotation
    )
  }

  // 绘制正在绘制的标注
  if (isDrawing.value && drawingPoints.value.length > 0) {
    canvasRenderer.value.drawDrawingAnnotation(
      drawingPoints.value,
      currentTool.value,
      canvasScale.value,
      canvasOffset.value,
      appStore.settings.annotation
    )
  }

  // 绘制标尺
  if (showRuler.value) {
    canvasRenderer.value.drawRuler(canvasScale.value, canvasOffset.value)
  }
}

const handleMouseDown = (event: MouseEvent) => {
  if (!canvas.value) return

  const rect = canvas.value.getBoundingClientRect()
  const screenPoint = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  }
  const imagePoint = imageStore.screenToImage(screenPoint)

  lastMousePos.value = screenPoint

  if (currentTool.value === 'select') {
    // 选择模式
    const annotation = annotationStore.findAnnotationAt(imagePoint)
    if (annotation) {
      if (event.ctrlKey || event.metaKey) {
        annotationStore.toggleSelection(annotation.id)
      } else {
        annotationStore.selectAnnotation(annotation.id)
      }
    } else {
      if (!event.ctrlKey && !event.metaKey) {
        annotationStore.clearSelection()
      }
    }
    isDragging.value = true
  } else if (currentTool.value === 'pan') {
    // 平移模式
    isDragging.value = true
  } else {
    // 绘制模式
    isDrawing.value = true
    drawingPoints.value = [imagePoint]
  }

  imageStore.setDragging(isDragging.value)
  imageStore.setDrawing(isDrawing.value)
}

const handleMouseMove = (event: MouseEvent) => {
  if (!canvas.value) return

  const rect = canvas.value.getBoundingClientRect()
  const screenPoint = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  }
  const imagePoint = imageStore.screenToImage(screenPoint)

  if (isDragging.value) {
    if (currentTool.value === 'select' && annotationStore.hasSelection) {
      // 移动选中的标注
      const deltaX = screenPoint.x - lastMousePos.value.x
      const deltaY = screenPoint.y - lastMousePos.value.y
      const imageDelta = {
        x: deltaX / canvasScale.value,
        y: deltaY / canvasScale.value
      }
      annotationStore.moveAnnotations([...selectedIds.value], imageDelta)
    } else {
      // 平移画布
      const deltaX = screenPoint.x - lastMousePos.value.x
      const deltaY = screenPoint.y - lastMousePos.value.y
      imageStore.panCanvas(deltaX, deltaY)
    }
    lastMousePos.value = screenPoint
  } else if (isDrawing.value) {
    // 更新绘制点
    if (currentTool.value === 'main-question' || 
        currentTool.value === 'sub-question' ||
        currentTool.value === 'answer-area' ||
        currentTool.value === 'image-area' ||
        currentTool.value === 'question-text') {
      
      if (drawingPoints.value.length === 1) {
        // 矩形绘制：更新第二个点
        drawingPoints.value[1] = imagePoint
      } else if (drawingPoints.value.length > 1) {
        // 多边形绘制：添加新点
        drawingPoints.value.push(imagePoint)
      }
    }
  }

  renderCanvas()
}

const handleMouseUp = () => {
  if (isDrawing.value && drawingPoints.value.length >= 2) {
    // 完成标注绘制
    const newAnnotation = {
      type: currentTool.value as any,
      coordinates: [...drawingPoints.value],
      attributes: {
        content: '',
        number: annotationStore.getNextNumber(currentTool.value as any)
      },
      parentId: undefined
    }

    annotationStore.addAnnotation(newAnnotation)
  }

  isDragging.value = false
  isDrawing.value = false
  drawingPoints.value = []

  imageStore.setDragging(false)
  imageStore.setDrawing(false)

  renderCanvas()
}

const handleWheel = (event: WheelEvent) => {
  event.preventDefault()

  if (event.ctrlKey || event.metaKey) {
    // 缩放
    const delta = event.deltaY > 0 ? 0.9 : 1.1
    const newScale = canvasScale.value * delta
    imageStore.setCanvasScale(newScale)
  } else {
    // 滚动
    const deltaX = event.deltaX
    const deltaY = event.deltaY
    imageStore.panCanvas(-deltaX, -deltaY)
  }

  renderCanvas()
}

const openImages = async () => {
  try {
    await imageStore.openImages()
  } catch (error) {
    console.error('Failed to open images:', error)
  }
}

const fitToCanvas = () => {
  imageStore.fitToCanvas()
  renderCanvas()
}

const resetZoom = () => {
  imageStore.resetZoom()
  renderCanvas()
}

const toggleGrid = () => {
  showGrid.value = !showGrid.value
  renderCanvas()
}

const toggleRuler = () => {
  showRuler.value = !showRuler.value
  renderCanvas()
}

const handleResize = () => {
  nextTick(() => {
    initCanvas()
  })
}

// 监听器
watch(currentImage, async (newImage) => {
  if (newImage) {
    isLoading.value = true
    try {
      await imageStore.cacheImage(newImage)
      annotationStore.loadAnnotations(newImage.annotations)
      await nextTick()
      imageStore.fitToCanvas()
      renderCanvas()
    } catch (error) {
      console.error('Failed to load image:', error)
    } finally {
      isLoading.value = false
    }
  }
})

watch([canvasScale, canvasOffset, annotations, selectedIds], () => {
  renderCanvas()
})

// 生命周期
onMounted(() => {
  initCanvas()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.image-canvas-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--bg-color);
  position: relative;
  
  .canvas-wrapper {
    flex: 1;
    position: relative;
    overflow: hidden;
    
    .image-canvas {
      width: 100%;
      height: 100%;
      cursor: crosshair;
      
      &.dragging {
        cursor: move;
      }
      
      &.panning {
        cursor: grab;
        
        &:active {
          cursor: grabbing;
        }
      }
    }
    
    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      @include flex-center;
      flex-direction: column;
      gap: var(--spacing-md);
      background: rgba(255, 255, 255, 0.9);
      
      p {
        margin: 0;
        color: var(--text-secondary);
      }
    }
    
    .empty-state {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      @include flex-center;
      flex-direction: column;
      gap: var(--spacing-lg);
      color: var(--text-secondary);
      
      .el-icon {
        font-size: 64px;
        color: var(--text-light);
      }
      
      h3 {
        margin: 0;
        font-size: var(--font-size-lg);
        color: var(--text-color);
      }
      
      p {
        margin: 0;
        text-align: center;
        max-width: 300px;
        line-height: 1.6;
      }
    }
  }
  
  .canvas-toolbar {
    height: 40px;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    @include flex-between;
    padding: 0 var(--spacing-md);
    
    .toolbar-left, .toolbar-right {
      @include flex-start;
    }
    
    .toolbar-center {
      @include flex-center;
      
      .zoom-level {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        min-width: 50px;
        text-align: center;
      }
    }
  }
}
</style>
