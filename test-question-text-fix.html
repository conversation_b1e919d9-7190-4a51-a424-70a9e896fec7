<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题干文字框测试</title>
</head>
<body>
    <h1>题干文字框与JSON对应测试</h1>
    
    <div id="test-results"></div>
    
    <script src="js/utils.js"></script>
    <script src="js/data-manager.js"></script>
    
    <script>
        // 测试数据
        const testJSON = {
            "大题1": {
                "坐标": [[55, 750], [786, 1139]],
                "题型": "填空题",
                "题干文字": "这是测试题干文字内容",
                "题目是否带配图": "否"
            }
        };
        
        // 创建DataManager实例
        const dataManager = new DataManager();
        
        // 测试1: JSON转标注
        console.log("测试1: JSON转标注");
        const annotations = dataManager.parseJSONToAnnotations(testJSON);
        console.log("生成的标注:", annotations);
        
        // 验证是否正确创建了大题和题干文字框
        const mainQuestion = annotations.find(ann => ann.type === 'main-question');
        const questionText = annotations.find(ann => ann.type === 'question-text');
        
        console.log("大题标注:", mainQuestion);
        console.log("题干文字框标注:", questionText);
        
        // 测试2: 标注转JSON
        console.log("\n测试2: 标注转JSON");
        const generatedJSON = dataManager.generateJSONFromAnnotations(
            { name: "test.jpg" },
            annotations,
            {}
        );
        console.log("生成的JSON:", generatedJSON);
        
        // 验证结果
        const results = document.getElementById('test-results');
        let html = '<h2>测试结果</h2>';
        
        // 检查是否正确创建了题干文字框
        if (questionText && questionText.type === 'question-text') {
            html += '<p style="color: green;">✓ 正确创建了题干文字框</p>';
            html += `<p>题干文字框内容: "${questionText.attributes.content}"</p>`;
        } else {
            html += '<p style="color: red;">✗ 未能创建题干文字框</p>';
        }
        
        // 检查大题是否正确关联
        if (mainQuestion && questionText && questionText.parentId === mainQuestion.id) {
            html += '<p style="color: green;">✓ 题干文字框正确关联到大题</p>';
        } else {
            html += '<p style="color: red;">✗ 题干文字框未正确关联到大题</p>';
        }
        
        // 检查JSON生成是否正确
        if (generatedJSON && generatedJSON["大题1"] && generatedJSON["大题1"]["题干文字"] === "这是测试题干文字内容") {
            html += '<p style="color: green;">✓ JSON中的题干文字正确</p>';
        } else {
            html += '<p style="color: red;">✗ JSON中的题干文字不正确</p>';
            html += `<p>实际值: "${generatedJSON?.["大题1"]?.["题干文字"]}"</p>`;
        }
        
        // 检查大题本身是否不保存题干文字
        if (mainQuestion && mainQuestion.attributes.content === '') {
            html += '<p style="color: green;">✓ 大题本身不保存题干文字</p>';
        } else {
            html += '<p style="color: red;">✗ 大题仍在保存题干文字</p>';
            html += `<p>大题content值: "${mainQuestion?.attributes?.content}"</p>`;
        }
        
        results.innerHTML = html;
        
        // 在控制台输出详细信息
        console.log("\n=== 详细测试结果 ===");
        console.log("原始JSON:", testJSON);
        console.log("解析后的标注:", annotations);
        console.log("重新生成的JSON:", generatedJSON);
    </script>
</body>
</html>
