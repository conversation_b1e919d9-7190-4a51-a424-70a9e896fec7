/**
 * OCR标注工具 - 主控制器
 * 集成所有模块，实现完整的应用逻辑
 */

class OCRAnnotationTool {
    constructor() {
        this.imageManager = null;
        this.annotationManager = null;
        this.dataManager = null;
        this.qualityCheckManager = null;
        this.uiManager = null;
        
        this.currentImageId = null;
        this.annotationData = new Map(); // 存储每张图片的标注数据
        
        this.init();
    }

    /**
     * 初始化应用
     */
    async init() {
        try {
            // 初始化管理器
            this.imageManager = new ImageManager();
            this.dataManager = new DataManager();
            this.qualityCheckManager = new QualityCheckManager();
            this.workspaceManager = new WorkspaceManager();
            this.ocrService = new OCRService();
            this.uiManager = window.uiManager; // 使用全局UI管理器实例

            // 等待图片元素加载完成后初始化标注管理器
            await this.waitForImageElement();
            this.annotationManager = new AnnotationManager(this.imageManager.getCoordinateSystem());

            // 设置事件监听器
            this.setupEventListeners();

            console.log('OCR标注工具初始化完成');
            Utils.showNotification('应用初始化完成', 'success');

        } catch (error) {
            console.error('初始化失败:', error);
            Utils.showNotification('应用初始化失败', 'error');
        }
    }

    /**
     * 等待图片元素加载完成
     */
    waitForImageElement() {
        return new Promise((resolve) => {
            const checkElement = () => {
                const imageElement = document.getElementById('currentImage');
                const canvasElement = document.getElementById('annotationCanvas');
                if (imageElement && canvasElement) {
                    resolve();
                } else {
                    setTimeout(checkElement, 100);
                }
            };
            checkElement();
        });
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 图片管理器事件
        this.imageManager.on('onImageChange', (image) => {
            this.onImageChange(image);
        });
        
        this.imageManager.on('onZoomChange', (zoom) => {
            this.onZoomChange(zoom);
        });
        
        this.imageManager.on('onImagesLoad', (images) => {
            this.onImagesLoad(images);
        });

        this.imageManager.on('onPanEnd', () => {
            this.onPanEnd();
        });

        this.imageManager.on('onPanUpdate', () => {
            this.onPanUpdate();
        });

        // 标注管理器事件
        this.annotationManager.on('onAnnotationCreate', (annotation) => {
            this.onAnnotationCreate(annotation);
        });
        
        this.annotationManager.on('onAnnotationUpdate', (annotation) => {
            this.onAnnotationUpdate(annotation);
        });
        
        this.annotationManager.on('onAnnotationDelete', (annotation) => {
            this.onAnnotationDelete(annotation);
        });
        
        this.annotationManager.on('onAnnotationSelect', (annotation) => {
            this.onAnnotationSelect(annotation);
        });

        // 数据管理器事件
        this.dataManager.on('onDataLoad', (data) => {
            this.onDataLoad(data);
        });
        
        this.dataManager.on('onDataSave', (data) => {
            this.onDataSave(data);
        });

        // 质检管理器事件
        this.qualityCheckManager.on('onModeChange', (data) => {
            this.onQualityModeChange(data);
        });

        // 工作区管理器事件
        this.workspaceManager.on('onWorkspaceLoad', (data) => {
            this.onWorkspaceLoad(data);
        });

        this.workspaceManager.on('onJsonSave', (data) => {
            this.onJsonSave(data);
        });

        // UI事件
        this.setupUIEventListeners();

        // 按钮点击事件
        this.setupButtonEvents();
    }

    /**
     * 设置UI事件监听器
     */
    setupUIEventListeners() {
        document.addEventListener('toolSelect', (e) => {
            this.annotationManager.setCurrentTool(e.detail.tool);
        });

        // 移除了左侧面板大题信息表单的事件监听器
        // 大题信息现在只在右侧编辑表单中处理

        document.addEventListener('modeChange', (e) => {
            this.switchMode(e.detail.mode);
        });

        document.addEventListener('shortcut', (e) => {
            this.handleShortcut(e.detail);
        });

        document.addEventListener('toggleAnnotations', () => {
            this.toggleAnnotationsVisibility();
        });

        // 移除annotationUpdate事件监听器，现在直接在UI管理器中更新内存

        document.addEventListener('annotationDelete', (e) => {
            this.annotationManager.deleteAnnotation(e.detail.annotationId);
        });

        document.addEventListener('annotationSelect', (e) => {
            if (e.detail.annotationId) {
                const annotation = this.annotationManager.getAnnotationById(e.detail.annotationId);
                this.annotationManager.selectAnnotation(annotation);
            }
        });

        document.addEventListener('annotationSelect', (e) => {
            if (e.detail.annotationId) {
                const annotation = this.annotationManager.getAllAnnotations()
                    .find(ann => ann.id === e.detail.annotationId);
                if (annotation) {
                    this.annotationManager.selectAnnotation(annotation);
                }
            }
        });

        document.addEventListener('getChildAnnotations', (e) => {
            this.handleGetChildAnnotations(e.detail);
        });

        // OCR事件监听器
        document.addEventListener('ocrStart', (e) => {
            this.handleOCRStart(e.detail);
        });

        document.addEventListener('ocrCancel', () => {
            this.handleOCRCancel();
        });

        document.addEventListener('saveSettings', (e) => {
            this.handleSaveSettings(e.detail);
        });

        document.addEventListener('requestCurrentConfig', (e) => {
            this.handleRequestCurrentConfig();
        });

        document.addEventListener('requestAvailableModels', () => {
            this.handleRequestAvailableModels();
        });
    }



    /**
     * 设置按钮事件
     */
    setupButtonEvents() {

        // 图片导航
        const prevImage = document.getElementById('prevImage');
        const nextImage = document.getElementById('nextImage');
        if (prevImage) {
            prevImage.addEventListener('click', () => {
                this.imageManager.previousImage();
            });
        }
        if (nextImage) {
            nextImage.addEventListener('click', () => {
                this.imageManager.nextImage();
            });
        }

        // 缩放控制
        const zoomIn = document.getElementById('zoomIn');
        const zoomOut = document.getElementById('zoomOut');
        const zoomReset = document.getElementById('zoomReset');
        if (zoomIn) {
            zoomIn.addEventListener('click', () => {
                this.imageManager.zoomIn();
            });
        }
        if (zoomOut) {
            zoomOut.addEventListener('click', () => {
                this.imageManager.zoomOut();
            });
        }
        if (zoomReset) {
            zoomReset.addEventListener('click', () => {
                this.imageManager.resetZoom();
            });
        }

        // 清空所有数据
        const clearAll = document.getElementById('clearAll');
        if (clearAll) {
            clearAll.addEventListener('click', () => {
                this.clearAllData();
            });
        }
    }

    /**
     * 自动保存当前图片的标注数据
     */
    async autoSaveCurrentImage() {
        if (!this.currentImageId || !this.currentImageName) {
            return;
        }

        // 检查是否有工作区
        if (!this.workspaceManager || !this.workspaceManager.isWorkspaceLoaded) {
            return;
        }

        try {
            const currentImage = this.imageManager.getCurrentImage();
            if (!currentImage) return;

            const annotations = this.annotationManager.exportAnnotations();
            const mainQuestions = annotations.filter(ann => ann.type === 'main-question');

            // 获取当前图片在工作区中已存在的JSON文件
            const existingFiles = await this.workspaceManager.getImageJsonFiles(this.currentImageName);
            const existingQuestionNumbers = new Set(existingFiles.map(file => file.questionNumber));

            // 当前标注中的大题编号
            const currentQuestionNumbers = new Set(mainQuestions.map(q => q.number));

            // 删除不再存在的大题对应的JSON文件
            for (const questionNumber of existingQuestionNumbers) {
                if (!currentQuestionNumbers.has(questionNumber)) {
                    await this.workspaceManager.deleteJsonFromWorkspace(this.currentImageName, questionNumber);
                    console.log(`删除不存在的大题JSON文件: ${this.currentImageName}_${questionNumber}.json`);
                }
            }

            // 保存当前存在的大题
            let savedCount = 0;
            for (const mainQuestion of mainQuestions) {
                const questionData = this.dataManager.generateJSONFromAnnotations(
                    { name: this.currentImageName },
                    annotations.filter(ann =>
                        ann.type === 'main-question' && ann.id === mainQuestion.id ||
                        ann.parentId === mainQuestion.id ||
                        this.dataManager.isAnnotationInside(ann, mainQuestion)
                    ),
                    {}
                );

                const success = await this.workspaceManager.saveJsonToWorkspace(
                    this.currentImageName,
                    mainQuestion.number,
                    questionData
                );

                if (success) {
                    savedCount++;
                }
            }

            if (savedCount > 0 || existingQuestionNumbers.size > currentQuestionNumbers.size) {
                console.log(`自动保存完成: ${this.currentImageName}, 保存${savedCount}个大题, 删除${existingQuestionNumbers.size - currentQuestionNumbers.size}个文件`);
            }

        } catch (error) {
            console.error('自动保存失败:', error);
        }
    }

    /**
     * 图片改变事件处理
     * @param {Object} image 图片对象
     */
    async onImageChange(image) {
        if (image) {
            // 在切换图片前，先保存当前图片的更改
            await this.autoSaveCurrentImage();

            this.currentImageId = image.id;
            this.currentImageName = image.name; // 保存图片名称

            // 首先尝试从内存中恢复标注数据
            const imageKey = this.getImageKey(image);
            const annotationData = this.annotationData.get(imageKey);
            if (annotationData) {
                this.annotationManager.loadAnnotations(annotationData);
                console.log(`从内存恢复图片 ${image.name} 的标注数据`);
            } else {
                // 如果内存中没有，尝试从工作区加载JSON文件
                this.loadAnnotationsFromWorkspace(image);
            }

            // 更新UI
            this.updateAnnotationLists();
            this.uiManager.updateSelectedAnnotationInfo(null);

            // 更新工作区按钮状态
            this.updateWorkspaceButtonStates();
        }
    }

    /**
     * 从工作区加载标注数据
     * @param {Object} image 图片对象
     */
    async loadAnnotationsFromWorkspace(image) {
        if (!this.workspaceManager || !this.workspaceManager.isWorkspaceLoaded) {
            this.annotationManager.clear();
            return;
        }

        try {
            // 获取该图片的所有JSON文件
            const jsonFiles = this.workspaceManager.getJsonFilesForImage(image.name);

            if (jsonFiles.length > 0) {
                // 合并所有大题的标注数据
                const allAnnotations = [];

                for (const jsonFile of jsonFiles) {
                    // 从JSON数据转换为标注对象
                    const annotations = this.dataManager.parseJSONToAnnotations(jsonFile.data);
                    allAnnotations.push(...annotations);
                }

                if (allAnnotations.length > 0) {
                    this.annotationManager.loadAnnotations(allAnnotations);
                    console.log(`从工作区加载了图片 ${image.name} 的 ${allAnnotations.length} 个标注`);

                    // 保存到内存中以便下次快速访问
                    const imageKey = this.getImageKey(image);
                    this.annotationData.set(imageKey, allAnnotations);
                } else {
                    this.annotationManager.clear();
                }
            } else {
                this.annotationManager.clear();
            }
        } catch (error) {
            console.error('从工作区加载标注数据失败:', error);
            this.annotationManager.clear();
        }
    }

    /**
     * 获取图片的唯一键（使用文件名和大小）
     * @param {Object} image 图片对象
     * @returns {string} 图片键
     */
    getImageKey(image) {
        // 使用文件名和文件大小作为唯一标识
        return `${image.name}_${image.size || 0}`;
    }

    /**
     * 缩放改变事件处理
     * @param {number} zoom 缩放级别
     */
    onZoomChange(zoom) {
        // 缩放改变时重绘标注
        if (this.annotationManager) {
            this.annotationManager.redraw();
        }
    }

    /**
     * 图片加载事件处理
     * @param {Array} images 图片数组
     */
    onImagesLoad(images) {
        // 图片加载完成后的处理
        console.log(`加载了${images.length}张图片`);
    }

    /**
     * 平移结束事件处理
     */
    onPanEnd() {
        // 平移结束后重绘标注，确保标注框正确显示
        if (this.annotationManager) {
            this.annotationManager.redraw();
        }
    }

    /**
     * 平移更新事件处理
     */
    onPanUpdate() {
        // 平移过程中实时重绘标注，确保标注框跟随移动
        if (this.annotationManager) {
            this.annotationManager.redraw();
        }
    }

    /**
     * 标注创建事件处理
     * @param {Object} annotation 标注对象
     */
    onAnnotationCreate(annotation) {
        this.saveCurrentAnnotations();
        this.updateAnnotationLists();
        this.uiManager.updateSelectedAnnotationInfo(annotation);
        
        // 如果是大题，更新表单
        if (annotation.type === 'main-question') {
            this.uiManager.updateMainQuestionForm(annotation);
        }
    }

    /**
     * 标注更新事件处理（主要处理坐标变化等非表单更新）
     * @param {Object} annotation 标注对象
     */
    onAnnotationUpdate(annotation) {
        // 保存到内存（处理坐标变化、移动、调整大小等操作）
        this.saveCurrentAnnotations();

        // 只在必要时更新导航列表（避免频繁更新）
        if (annotation && (annotation.type === 'main-question' || annotation.type === 'sub-question')) {
            this.updateAnnotationLists();
        }

        // 注意：不在这里调用generateReferences，避免循环调用
        // generateReferences会在其他适当的时机调用
    }

    /**
     * 标注删除事件处理
     * @param {Object} annotation 标注对象
     */
    onAnnotationDelete(annotation) {
        this.saveCurrentAnnotations();
        this.updateAnnotationLists();
        
        // 如果删除的是大题，清空表单
        if (annotation.type === 'main-question') {
            this.uiManager.updateMainQuestionForm(null);
        }
    }

    /**
     * 标注选择事件处理
     * @param {Object} annotation 标注对象
     */
    onAnnotationSelect(annotation) {
        this.uiManager.updateSelectedAnnotationInfo(annotation);
    }

    /**
     * 数据加载事件处理
     * @param {Object} data 数据对象
     */
    onDataLoad(data) {
        console.log('数据加载完成');
    }

    /**
     * 数据保存事件处理
     * @param {Object} data 数据对象
     */
    onDataSave(data) {
        console.log('数据保存完成');
    }

    /**
     * 工作区加载事件处理
     * @param {Object} data 工作区数据
     */
    onWorkspaceLoad(data) {
        console.log('工作区加载完成:', data);

        // 更新UI状态
        this.uiManager.updateWorkspaceStatus(data);

        // 更新工作区按钮状态
        this.updateWorkspaceButtonStates();

        // 自动加载工作区中的图片
        this.loadImagesFromWorkspace();
    }

    /**
     * JSON保存事件处理
     * @param {Object} data JSON保存数据
     */
    onJsonSave(data) {
        console.log('JSON文件保存完成:', data.filename);

        // 更新UI状态
        this.uiManager.updateWorkspaceStatus(this.workspaceManager.getWorkspaceStatus());
    }

    /**
     * 从工作区加载图片
     */
    async loadImagesFromWorkspace() {
        try {
            const images = await this.workspaceManager.loadImagesFromWorkspace();
            if (images.length > 0) {
                // 使用新的loadImageObjects方法
                await this.imageManager.loadImageObjects(images);
            } else {
                Utils.showNotification('工作区中没有找到图片文件', 'warning');
            }
        } catch (error) {
            console.error('从工作区加载图片失败:', error);
            Utils.showNotification('从工作区加载图片失败', 'error');
        }
    }

    /**
     * 质检模式改变事件处理
     * @param {Object} data 模式数据
     */
    onQualityModeChange(data) {
        this.uiManager.setMode(data.mode);
        
        if (data.mode === 'quality-check') {
            // 进入质检模式，禁用标注功能
            this.annotationManager.setCurrentTool('');
        }
    }

    /**
     * 更新大题属性
     * @param {string} attribute 属性名
     * @param {any} value 属性值
     */
    updateMainQuestionAttribute(attribute, value) {
        const mainQuestion = this.annotationManager.getAnnotationsByType('main-question')[0];
        if (mainQuestion) {
            this.annotationManager.updateAnnotation(mainQuestion.id, { [attribute]: value });
        }
    }

    /**
     * 切换模式
     * @param {string} mode 模式
     */
    switchMode(mode) {
        if (mode === 'quality-check') {
            // 进入质检模式
            const currentData = this.getCurrentJSONData();
            if (currentData) {
                this.qualityCheckManager.enterQualityCheckMode(currentData, this.currentImageId);
            } else {
                Utils.showNotification('当前图片没有标注数据', 'warning');
                this.uiManager.setMode('annotation');
            }
        } else {
            // 退出质检模式
            this.qualityCheckManager.exitQualityCheckMode();
        }
    }

    /**
     * 切换标注显示状态
     */
    toggleAnnotationsVisibility() {
        if (this.annotationManager) {
            const isVisible = this.annotationManager.toggleAnnotationsVisibility();
            this.uiManager.updateToggleAnnotationsButton(isVisible);

            const message = isVisible ? '标注已显示' : '标注已隐藏';
            Utils.showNotification(message, 'info');
        }
    }

    /**
     * 处理快捷键
     * @param {Object} shortcut 快捷键信息
     */
    handleShortcut(shortcut) {
        switch (shortcut.action) {
            case 'main-question':
            case 'question-text':
            case 'sub-question':
            case 'answer-area':
            case 'image-area':
                this.annotationManager.setCurrentTool(shortcut.action);
                this.uiManager.updateToolButtonStates(shortcut.action);
                break;
                
            case 'prevImage':
                this.imageManager.previousImage();
                break;
                
            case 'nextImage':
                this.imageManager.nextImage();
                break;
                
            case 'zoomIn':
                this.imageManager.zoomIn();
                break;
                
            case 'zoomOut':
                this.imageManager.zoomOut();
                break;
                
            case 'resetZoom':
                this.imageManager.resetZoom();
                break;
                
            case 'toggleQualityCheck':
                const currentMode = this.uiManager.getCurrentMode();
                this.switchMode(currentMode === 'quality-check' ? 'annotation' : 'quality-check');
                break;

            case 'toggleAnnotations':
                this.toggleAnnotationsVisibility();
                break;

            case 'showShortcuts':
                Utils.showShortcutsHelp();
                break;

            case 'clearSelection':
                this.annotationManager.clearSelection();
                this.uiManager.updateToolButtonStates('');
                break;
                
            case 'deleteSelected':
                const selected = this.annotationManager.getSelectedAnnotation();
                if (selected) {
                    this.annotationManager.deleteAnnotation(selected.id);
                }
                break;
                
            case 'save':
                this.uiManager.saveAllToWorkspace();
                break;
        }
    }

    /**
     * 处理获取子标注请求
     * @param {Object} detail 请求详情
     */
    handleGetChildAnnotations(detail) {
        const { parentId, type } = detail;
        const childAnnotations = this.annotationManager.getChildAnnotations(parentId, type);

        // 将结果存储到UI管理器中
        this.uiManager.tempChildAnnotations = childAnnotations;
    }

    /**
     * 更新标注列表显示
     */
    updateAnnotationLists() {
        const annotations = this.annotationManager.exportAnnotations();
        const mainQuestions = this.annotationManager.getAnnotationsByType('main-question');
        this.uiManager.updateQuestionCounter(mainQuestions.length);

        // 更新题目导航
        this.uiManager.updateQuestionNavigation(annotations);

        // 更新工作区按钮状态
        this.updateWorkspaceButtonStates();

        // 层级内容现在通过选中标注时动态显示，不需要全局更新
    }

    /**
     * 更新工作区按钮状态
     */
    updateWorkspaceButtonStates() {
        const hasWorkspace = this.workspaceManager && this.workspaceManager.isWorkspaceLoaded;
        const hasAnnotations = this.annotationManager && this.annotationManager.getAllAnnotations().length > 0;

        if (this.uiManager && this.uiManager.updateWorkspaceButtons) {
            this.uiManager.updateWorkspaceButtons(hasWorkspace, hasAnnotations);
        }
    }

    /**
     * 保存当前标注数据
     */
    saveCurrentAnnotations() {
        if (this.currentImageId && this.currentImageName) {
            const annotations = this.annotationManager.exportAnnotations();
            const currentImage = this.imageManager.getCurrentImage();
            const imageKey = this.getImageKey(currentImage);
            this.annotationData.set(imageKey, annotations);

            console.log(`保存图片 ${this.currentImageName} 的标注数据到内存`);
        }
    }

    /**
     * 获取当前JSON数据
     * @returns {Object|null} JSON数据
     */
    getCurrentJSONData() {
        if (!this.currentImageId) return null;
        
        const currentImage = this.imageManager.getCurrentImage();
        const annotations = this.annotationManager.getAllAnnotations();
        const questionInfo = this.getQuestionInfo();
        
        if (annotations.length === 0) return null;
        
        try {
            return this.dataManager.generateJSONFromAnnotations(
                currentImage, 
                annotations, 
                questionInfo
            );
        } catch (error) {
            console.error('生成JSON数据失败:', error);
            return null;
        }
    }

    /**
     * 获取题目信息（从大题标注对象获取，而不是从表单）
     * @returns {Object} 题目信息
     */
    getQuestionInfo() {
        const mainQuestion = this.annotationManager.getAnnotationsByType('main-question')[0];
        if (mainQuestion) {
            return {
                questionType: mainQuestion.attributes.questionType || '填空题',
                questionContent: mainQuestion.attributes.content || '',
                hasImage: mainQuestion.attributes.hasImage || false
            };
        }
        return {
            questionType: '填空题',
            questionContent: '',
            hasImage: false
        };
    }

    /**
     * 从JSON加载标注
     * @param {Object} jsonData JSON数据
     */
    loadAnnotationsFromJSON(jsonData) {
        try {
            const annotations = this.dataManager.parseAnnotationsFromJSON(jsonData);
            this.annotationManager.loadAnnotations(annotations);
            
            // 更新UI
            this.updateAnnotationLists();
            const mainQuestion = annotations.find(ann => ann.type === 'main-question');
            if (mainQuestion) {
                this.uiManager.updateMainQuestionForm(mainQuestion);
            }
            
            // 保存到当前图片
            this.saveCurrentAnnotations();
            
            Utils.showNotification('标注数据加载成功', 'success');
        } catch (error) {
            console.error('加载标注数据失败:', error);
            Utils.showNotification('加载标注数据失败', 'error');
        }
    }



    /**
     * 清空所有数据
     */
    clearAllData() {
        Utils.showConfirm(
            '确定要清空所有标注数据吗？此操作不可撤销。',
            () => {
                this.annotationManager.clear();
                this.annotationData.clear();
                this.dataManager.clearData();
                this.uiManager.updateMainQuestionForm(null);
                this.updateAnnotationLists();
                Utils.showNotification('所有数据已清空', 'success');
            }
        );
    }

    /**
     * 处理OCR开始事件
     * @param {Object} detail 事件详情
     */
    async handleOCRStart(detail) {
        const { mainQuestionId } = detail;

        try {
            // 获取大题标注
            const mainQuestionAnnotation = this.annotationManager.getAnnotationById(mainQuestionId);
            if (!mainQuestionAnnotation) {
                Utils.showNotification('找不到指定的大题', 'error');
                this.uiManager.showMainQuestionOCRLoading(mainQuestionId, false);
                return;
            }

            // 获取所有标注
            const allAnnotations = this.annotationManager.getAllAnnotations();

            // 获取图片元素
            const imageElement = this.imageManager.imageElement;
            if (!imageElement) {
                Utils.showNotification('图片未加载', 'error');
                this.uiManager.showMainQuestionOCRLoading(mainQuestionId, false);
                return;
            }

            Utils.showNotification('开始OCR处理...', 'info');

            // 设置进度回调
            const progressCallback = (progress) => {
                this.uiManager.updateOCRProgress(progress);
            };

            // 执行OCR处理
            const results = await this.ocrService.processMainQuestionOCR(
                mainQuestionAnnotation,
                allAnnotations,
                imageElement,
                this.ocrService.getModel(), // 使用当前设置的模型
                progressCallback
            );

            // 隐藏加载状态和进度条
            this.uiManager.showMainQuestionOCRLoading(mainQuestionId, false);
            this.uiManager.hideOCRProgress();

            // 显示结果
            this.uiManager.showOCRResults(results);

            // 更新标注内容
            let updatedCount = 0;
            results.forEach(result => {
                if (result.success && result.ocrText) {
                    const annotation = this.annotationManager.getAnnotationById(result.annotationId);
                    if (annotation) {
                        // 根据标注类型更新相应字段
                        switch (result.type) {
                            case 'main-question':
                            case 'question-text':
                            case 'sub-question':
                                annotation.attributes.content = result.ocrText;
                                break;
                            case 'answer-area':
                                annotation.attributes.answerContent = result.ocrText;
                                break;
                            case 'image-area':
                                annotation.attributes.imageDescription = result.ocrText;
                                break;
                        }
                        updatedCount++;
                    }
                }
            });

            Utils.showNotification(`OCR处理完成，已更新 ${updatedCount} 个标注`, 'success');

            // 保存OCR识别结果到内存
            this.saveCurrentAnnotations();

            // 刷新UI显示
            this.updateAnnotationLists();

            // 如果当前选中的标注被更新了，刷新右侧面板
            if (this.uiManager.selectedAnnotation) {
                const selectedAnnotation = this.uiManager.selectedAnnotation;
                const updatedAnnotation = this.annotationManager.getAnnotationById(selectedAnnotation.id);
                if (updatedAnnotation) {
                    this.uiManager.updateSelectedAnnotationInfo(updatedAnnotation);
                } else if (selectedAnnotation.type === 'main-question') {
                    // 如果选中的是大题，可能其子题干文字框被更新了，需要刷新表单
                    this.uiManager.updateSelectedAnnotationInfo(selectedAnnotation);
                }
            }

        } catch (error) {
            console.error('OCR处理失败:', error);
            Utils.showNotification(`OCR处理失败: ${error.message}`, 'error');
            this.uiManager.showMainQuestionOCRLoading(mainQuestionId, false);
            this.uiManager.hideOCRProgress();
        }
    }

    /**
     * 处理OCR取消事件
     */
    handleOCRCancel() {
        // 取消OCR处理
        if (this.ocrService && this.ocrService.cancelCurrentOCR) {
            this.ocrService.cancelCurrentOCR();
        }

        this.uiManager.hideOCRProgress();
        Utils.showNotification('OCR处理已取消', 'info');
    }

    /**
     * 处理保存设置事件
     * @param {Object} detail 事件详情
     */
    handleSaveSettings(detail) {
        // 更新OCR服务配置
        if (this.ocrService) {
            // 设置模型
            this.ocrService.setModel(detail.model);

            // 设置批量配置
            this.ocrService.setBatchConfig({
                useTrueBatch: detail.useTrueBatch
            });

            console.log('设置已保存:', {
                model: detail.model,
                useTrueBatch: detail.useTrueBatch
            });
        }
    }

    /**
     * 处理请求当前配置事件
     */
    handleRequestCurrentConfig() {
        if (this.ocrService) {
            const batchConfig = this.ocrService.getBatchConfig();
            const currentModel = this.ocrService.getModel();

            // 更新设置弹窗中的值
            const modelElement = document.getElementById('settingsOcrModel');
            const useTrueBatchElement = document.getElementById('settingsUseTrueBatch');

            if (modelElement && currentModel) {
                modelElement.value = currentModel;
            }
            if (useTrueBatchElement) {
                useTrueBatchElement.checked = batchConfig.useTrueBatch;
            }
        }
    }

    /**
     * 处理请求可用模型事件
     */
    handleRequestAvailableModels() {
        if (this.ocrService) {
            const availableModels = this.ocrService.getAvailableModels();

            // 更新UI中的模型选项
            this.uiManager.updateModelOptions(availableModels);
        }
    }


}

// 应用启动
document.addEventListener('DOMContentLoaded', () => {
    window.ocrTool = new OCRAnnotationTool();
});
