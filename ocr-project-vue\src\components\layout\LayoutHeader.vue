<template>
  <header class="layout-header">
    <div class="header-left">
      <div class="logo">
        <el-icon><Picture /></el-icon>
        <span class="logo-text">OCR标注工具</span>
      </div>
      
      <div class="toolbar">
        <el-button-group>
          <el-button 
            :type="currentTool === 'select' ? 'primary' : 'default'"
            @click="setTool('select')"
            title="选择工具 (V)"
          >
            <el-icon><Pointer /></el-icon>
          </el-button>
          
          <el-button 
            :type="currentTool === 'main-question' ? 'primary' : 'default'"
            @click="setTool('main-question')"
            title="大题工具 (Q)"
          >
            <el-icon><Document /></el-icon>
            大题
          </el-button>
          
          <el-button 
            :type="currentTool === 'sub-question' ? 'primary' : 'default'"
            @click="setTool('sub-question')"
            title="小题工具 (W)"
          >
            <el-icon><List /></el-icon>
            小题
          </el-button>
          
          <el-button 
            :type="currentTool === 'answer-area' ? 'primary' : 'default'"
            @click="setTool('answer-area')"
            title="答题区域工具 (E)"
          >
            <el-icon><Edit /></el-icon>
            答题区域
          </el-button>
          
          <el-button 
            :type="currentTool === 'image-area' ? 'primary' : 'default'"
            @click="setTool('image-area')"
            title="配图区域工具 (R)"
          >
            <el-icon><Picture /></el-icon>
            配图区域
          </el-button>
          
          <el-button 
            :type="currentTool === 'question-text' ? 'primary' : 'default'"
            @click="setTool('question-text')"
            title="题干文字工具 (T)"
          >
            <el-icon><ChatLineRound /></el-icon>
            题干文字
          </el-button>
        </el-button-group>
        
        <el-divider direction="vertical" />
        
        <el-button-group>
          <el-button @click="undo" :disabled="!canUndo" title="撤销">
            <el-icon><RefreshLeft /></el-icon>
          </el-button>
          <el-button @click="redo" :disabled="!canRedo" title="重做">
            <el-icon><RefreshRight /></el-icon>
          </el-button>
        </el-button-group>
        
        <el-divider direction="vertical" />
        
        <el-button-group>
          <el-button @click="zoomOut" title="缩小">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
          <el-button @click="resetZoom" title="适应画布">
            <el-icon><FullScreen /></el-icon>
          </el-button>
          <el-button @click="zoomIn" title="放大">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
        </el-button-group>
      </div>
    </div>
    
    <div class="header-center">
      <div class="image-nav" v-if="hasImages">
        <el-button 
          @click="navigateToFirst" 
          :disabled="!canNavigatePrev"
          size="small"
        >
          <el-icon><DArrowLeft /></el-icon>
        </el-button>
        <el-button 
          @click="navigatePrev" 
          :disabled="!canNavigatePrev"
          size="small"
        >
          <el-icon><ArrowLeft /></el-icon>
        </el-button>
        
        <span class="image-counter">
          {{ currentImageNumber }} / {{ imageCount }}
        </span>
        
        <el-button 
          @click="navigateNext" 
          :disabled="!canNavigateNext"
          size="small"
        >
          <el-icon><ArrowRight /></el-icon>
        </el-button>
        <el-button 
          @click="navigateToLast" 
          :disabled="!canNavigateNext"
          size="small"
        >
          <el-icon><DArrowRight /></el-icon>
        </el-button>
      </div>
      
      <div class="current-image-name" v-if="currentImage">
        {{ currentImage.name }}
      </div>
    </div>
    
    <div class="header-right">
      <el-button-group>
        <el-button @click="openImages" title="打开图片 (Ctrl+O)">
          <el-icon><FolderOpened /></el-icon>
          打开图片
        </el-button>
        <el-button @click="saveData" title="保存 (Ctrl+S)" :loading="isLoading">
          <el-icon><DocumentCopy /></el-icon>
          保存
        </el-button>
        <el-button @click="exportJSON" title="导出JSON (Ctrl+E)">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </el-button-group>
      
      <el-divider direction="vertical" />
      
      <el-dropdown @command="handleModeChange">
        <el-button>
          {{ mode === 'annotation' ? '标注模式' : '质检模式' }}
          <el-icon class="el-icon--right"><ArrowDown /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="annotation">标注模式</el-dropdown-item>
            <el-dropdown-item command="quality-check">质检模式</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </header>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  Picture, Pointer, Document, List, Edit, ChatLineRound,
  RefreshLeft, RefreshRight, ZoomOut, ZoomIn, FullScreen,
  DArrowLeft, ArrowLeft, ArrowRight, DArrowRight,
  FolderOpened, DocumentCopy, Download, ArrowDown
} from '@element-plus/icons-vue'
import { useAppStore } from '@/stores/app'
import { useImageStore } from '@/stores/image'
import { useAnnotationStore } from '@/stores/annotation'
import { useDataStore } from '@/stores/data'
import type { Tool } from '@/types'

// Store
const appStore = useAppStore()
const imageStore = useImageStore()
const annotationStore = useAnnotationStore()
const dataStore = useDataStore()

// 计算属性
const currentTool = computed(() => appStore.currentTool)
const mode = computed(() => appStore.mode)
const isLoading = computed(() => appStore.isLoading)

const hasImages = computed(() => imageStore.hasImages)
const currentImage = computed(() => imageStore.currentImage)
const currentImageNumber = computed(() => imageStore.currentImageNumber)
const imageCount = computed(() => imageStore.imageCount)
const canNavigatePrev = computed(() => imageStore.canNavigatePrev)
const canNavigateNext = computed(() => imageStore.canNavigateNext)

const canUndo = computed(() => annotationStore.canUndo)
const canRedo = computed(() => annotationStore.canRedo)

// 方法
const setTool = (tool: Tool) => {
  appStore.setTool(tool)
}

const undo = () => {
  annotationStore.undo()
}

const redo = () => {
  annotationStore.redo()
}

const zoomIn = () => {
  imageStore.zoomIn()
}

const zoomOut = () => {
  imageStore.zoomOut()
}

const resetZoom = () => {
  imageStore.fitToCanvas()
}

const navigateToFirst = () => {
  imageStore.navigateToFirst()
}

const navigatePrev = () => {
  imageStore.navigatePrev()
}

const navigateNext = () => {
  imageStore.navigateNext()
}

const navigateToLast = () => {
  imageStore.navigateToLast()
}

const openImages = async () => {
  try {
    await imageStore.openImages()
  } catch (error) {
    console.error('Failed to open images:', error)
  }
}

const saveData = async () => {
  try {
    await dataStore.saveCurrentData()
  } catch (error) {
    console.error('Failed to save data:', error)
  }
}

const exportJSON = async () => {
  try {
    await dataStore.exportJSON()
  } catch (error) {
    console.error('Failed to export JSON:', error)
  }
}

const handleModeChange = (command: string) => {
  appStore.setMode(command as 'annotation' | 'quality-check')
}
</script>

<style lang="scss" scoped>
.layout-header {
  height: var(--header-height);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  @include flex-between;
  padding: 0 var(--spacing-lg);
  box-shadow: var(--shadow-light);
  
  .header-left {
    @include flex-start;
    gap: var(--spacing-lg);
    
    .logo {
      @include flex-center;
      gap: var(--spacing-sm);
      font-size: var(--font-size-lg);
      font-weight: 600;
      color: var(--primary-color);
      
      .logo-text {
        white-space: nowrap;
      }
    }
    
    .toolbar {
      @include flex-start;
      gap: var(--spacing-sm);
    }
  }
  
  .header-center {
    @include flex-center;
    flex-direction: column;
    gap: var(--spacing-xs);
    
    .image-nav {
      @include flex-center;
      gap: var(--spacing-xs);
      
      .image-counter {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        min-width: 60px;
        text-align: center;
      }
    }
    
    .current-image-name {
      font-size: var(--font-size-sm);
      color: var(--text-color);
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  
  .header-right {
    @include flex-start;
    gap: var(--spacing-sm);
  }
}

.el-divider--vertical {
  height: 20px;
  margin: 0 var(--spacing-sm);
}
</style>
