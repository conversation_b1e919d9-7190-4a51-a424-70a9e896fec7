import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import type { AppState, Tool, MenuAction, Settings } from '@/types'
import { useImageStore } from './image'
import { useAnnotationStore } from './annotation'
import { useDataStore } from './data'

export const useAppStore = defineStore('app', () => {
  // 状态
  const loading = ref(false)
  const error = ref<string>()
  const currentTool = ref<Tool>('select')
  const mode = ref<'annotation' | 'quality-check'>('annotation')
  const sidebarCollapsed = ref(false)
  const panelCollapsed = ref(false)
  
  // 设置
  const settings = ref<Settings>({
    general: {
      language: 'zh-CN',
      theme: 'light',
      autoSave: true,
      saveInterval: 30000 // 30秒
    },
    canvas: {
      showGrid: false,
      showRuler: false,
      gridSize: 20,
      snapToGrid: false
    },
    annotation: {
      defaultColors: {
        'main-question': '#e74c3c',
        'sub-question': '#2ecc71',
        'answer-area': '#9b59b6',
        'image-area': '#f39c12',
        'question-text': '#3498db'
      },
      strokeWidth: 2,
      fontSize: 14,
      showLabels: true
    },
    ocr: {
      provider: 'doubao',
      useTrueBatch: false,
      batchSize: 10,
      timeout: 30000
    },
    shortcuts: [
      { key: 'q', action: 'tool-main-question', description: '大题工具' },
      { key: 'w', action: 'tool-sub-question', description: '小题工具' },
      { key: 'e', action: 'tool-answer-area', description: '答题区域工具' },
      { key: 'r', action: 'tool-image-area', description: '配图区域工具' },
      { key: 't', action: 'tool-question-text', description: '题干文字工具' },
      { key: 'v', action: 'tool-select', description: '选择工具' },
      { key: 'Delete', action: 'delete-annotation', description: '删除标注' },
      { key: 's', ctrlKey: true, action: 'save', description: '保存' },
      { key: 'o', ctrlKey: true, action: 'open-images', description: '打开图片' },
      { key: 'e', ctrlKey: true, action: 'export-json', description: '导出JSON' }
    ]
  })

  // 计算属性
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => !!error.value)
  const currentTheme = computed(() => settings.value.general.theme)
  const autoSaveEnabled = computed(() => settings.value.general.autoSave)

  // 获取其他store
  const imageStore = useImageStore()
  const annotationStore = useAnnotationStore()
  const dataStore = useDataStore()

  // 方法
  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const setError = (message?: string) => {
    error.value = message
  }

  const clearError = () => {
    error.value = undefined
  }

  const setTool = (tool: Tool) => {
    currentTool.value = tool
  }

  const setMode = (newMode: 'annotation' | 'quality-check') => {
    mode.value = newMode
  }

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  const togglePanel = () => {
    panelCollapsed.value = !panelCollapsed.value
  }

  const updateSettings = (newSettings: Partial<Settings>) => {
    settings.value = { ...settings.value, ...newSettings }
    saveSettings()
  }

  const saveSettings = async () => {
    try {
      if (window.electronAPI) {
        const settingsPath = await getSettingsPath()
        await window.electronAPI.fs.writeFile(settingsPath, JSON.stringify(settings.value, null, 2))
      } else {
        localStorage.setItem('ocr-tool-settings', JSON.stringify(settings.value))
      }
    } catch (err) {
      console.error('Failed to save settings:', err)
    }
  }

  const loadSettings = async () => {
    try {
      if (window.electronAPI) {
        const settingsPath = await getSettingsPath()
        const exists = await window.electronAPI.fs.exists(settingsPath)
        if (exists) {
          const result = await window.electronAPI.fs.readFile(settingsPath)
          if (result.success && result.data) {
            settings.value = { ...settings.value, ...JSON.parse(result.data) }
          }
        }
      } else {
        const saved = localStorage.getItem('ocr-tool-settings')
        if (saved) {
          settings.value = { ...settings.value, ...JSON.parse(saved) }
        }
      }
    } catch (err) {
      console.error('Failed to load settings:', err)
    }
  }

  const getSettingsPath = async (): Promise<string> => {
    // 获取用户数据目录下的设置文件路径
    return 'settings.json' // 简化实现，实际应该使用app.getPath('userData')
  }

  const handleMenuAction = async (action: MenuAction) => {
    try {
      setLoading(true)
      clearError()

      switch (action) {
        case 'open-images':
          await imageStore.openImages()
          break
        case 'open-folder':
          await imageStore.openFolder()
          break
        case 'save':
          await dataStore.saveCurrentData()
          break
        case 'export-json':
          await dataStore.exportJSON()
          break
        default:
          console.warn('Unknown menu action:', action)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '操作失败')
    } finally {
      setLoading(false)
    }
  }

  const handleKeyboardShortcut = (event: KeyboardEvent) => {
    const shortcut = settings.value.shortcuts.find(s => 
      s.key.toLowerCase() === event.key.toLowerCase() &&
      !!s.ctrlKey === event.ctrlKey &&
      !!s.shiftKey === event.shiftKey &&
      !!s.altKey === event.altKey
    )

    if (shortcut) {
      event.preventDefault()
      handleShortcutAction(shortcut.action)
    }
  }

  const handleShortcutAction = (action: string) => {
    switch (action) {
      case 'tool-main-question':
        setTool('main-question')
        break
      case 'tool-sub-question':
        setTool('sub-question')
        break
      case 'tool-answer-area':
        setTool('answer-area')
        break
      case 'tool-image-area':
        setTool('image-area')
        break
      case 'tool-question-text':
        setTool('question-text')
        break
      case 'tool-select':
        setTool('select')
        break
      case 'delete-annotation':
        annotationStore.deleteSelected()
        break
      case 'save':
        handleMenuAction('save')
        break
      case 'open-images':
        handleMenuAction('open-images')
        break
      case 'export-json':
        handleMenuAction('export-json')
        break
      default:
        console.warn('Unknown shortcut action:', action)
    }
  }

  const initialize = async () => {
    try {
      setLoading(true)
      await loadSettings()
      
      // 绑定键盘事件
      document.addEventListener('keydown', handleKeyboardShortcut)
      
      // 初始化其他store
      await dataStore.initialize()
      
    } catch (err) {
      setError(err instanceof Error ? err.message : '初始化失败')
    } finally {
      setLoading(false)
    }
  }

  const cleanup = () => {
    document.removeEventListener('keydown', handleKeyboardShortcut)
  }

  return {
    // 状态
    loading: readonly(loading),
    error: readonly(error),
    currentTool: readonly(currentTool),
    mode: readonly(mode),
    sidebarCollapsed: readonly(sidebarCollapsed),
    panelCollapsed: readonly(panelCollapsed),
    settings: readonly(settings),
    
    // 计算属性
    isLoading,
    hasError,
    currentTheme,
    autoSaveEnabled,
    
    // 方法
    setLoading,
    setError,
    clearError,
    setTool,
    setMode,
    toggleSidebar,
    togglePanel,
    updateSettings,
    handleMenuAction,
    initialize,
    cleanup
  }
})
