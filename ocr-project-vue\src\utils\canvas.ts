import type { Point, Annotation, AnnotationType, AnnotationSettings } from '@/types'

export class CanvasRenderer {
  private ctx: CanvasRenderingContext2D
  private canvas: HTMLCanvasElement

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      throw new Error('Failed to get 2D context')
    }
    this.ctx = ctx
  }

  drawGrid(scale: number, offset: Point) {
    const gridSize = 20 * scale
    const { width, height } = this.canvas

    this.ctx.save()
    this.ctx.strokeStyle = '#e0e0e0'
    this.ctx.lineWidth = 1
    this.ctx.globalAlpha = 0.5

    // 垂直线
    for (let x = offset.x % gridSize; x < width; x += gridSize) {
      this.ctx.beginPath()
      this.ctx.moveTo(x, 0)
      this.ctx.lineTo(x, height)
      this.ctx.stroke()
    }

    // 水平线
    for (let y = offset.y % gridSize; y < height; y += gridSize) {
      this.ctx.beginPath()
      this.ctx.moveTo(0, y)
      this.ctx.lineTo(width, y)
      this.ctx.stroke()
    }

    this.ctx.restore()
  }

  drawImage(image: HTMLImageElement, scale: number, offset: Point) {
    this.ctx.save()
    this.ctx.imageSmoothingEnabled = true
    this.ctx.imageSmoothingQuality = 'high'

    const width = image.width * scale
    const height = image.height * scale

    this.ctx.drawImage(
      image,
      offset.x,
      offset.y,
      width,
      height
    )

    this.ctx.restore()
  }

  drawAnnotations(
    annotations: readonly Annotation[],
    selectedIds: readonly string[],
    scale: number,
    offset: Point,
    settings: AnnotationSettings
  ) {
    annotations.forEach(annotation => {
      const isSelected = selectedIds.includes(annotation.id)
      this.drawAnnotation(annotation, isSelected, scale, offset, settings)
    })
  }

  drawAnnotation(
    annotation: Annotation,
    isSelected: boolean,
    scale: number,
    offset: Point,
    settings: AnnotationSettings
  ) {
    if (annotation.coordinates.length < 2) return

    const color = this.getAnnotationColor(annotation.type)
    const screenPoints = annotation.coordinates.map(point => ({
      x: point.x * scale + offset.x,
      y: point.y * scale + offset.y
    }))

    this.ctx.save()
    this.ctx.strokeStyle = isSelected ? '#ff4444' : color
    this.ctx.lineWidth = isSelected ? settings.strokeWidth + 1 : settings.strokeWidth
    this.ctx.fillStyle = isSelected ? 'rgba(255, 68, 68, 0.1)' : `${color}20`

    // 绘制多边形
    this.ctx.beginPath()
    this.ctx.moveTo(screenPoints[0].x, screenPoints[0].y)
    for (let i = 1; i < screenPoints.length; i++) {
      this.ctx.lineTo(screenPoints[i].x, screenPoints[i].y)
    }
    this.ctx.closePath()
    this.ctx.fill()
    this.ctx.stroke()

    // 绘制控制点
    if (isSelected) {
      this.drawControlPoints(screenPoints)
    }

    // 绘制标签
    if (settings.showLabels) {
      this.drawAnnotationLabel(annotation, screenPoints[0], settings)
    }

    this.ctx.restore()
  }

  drawDrawingAnnotation(
    points: Point[],
    type: AnnotationType,
    scale: number,
    offset: Point,
    settings: AnnotationSettings
  ) {
    if (points.length < 1) return

    const color = this.getAnnotationColor(type)
    const screenPoints = points.map(point => ({
      x: point.x * scale + offset.x,
      y: point.y * scale + offset.y
    }))

    this.ctx.save()
    this.ctx.strokeStyle = color
    this.ctx.lineWidth = settings.strokeWidth
    this.ctx.setLineDash([5, 5])

    if (screenPoints.length === 1) {
      // 绘制起始点
      this.ctx.beginPath()
      this.ctx.arc(screenPoints[0].x, screenPoints[0].y, 3, 0, Math.PI * 2)
      this.ctx.fill()
    } else {
      // 绘制线条
      this.ctx.beginPath()
      this.ctx.moveTo(screenPoints[0].x, screenPoints[0].y)
      for (let i = 1; i < screenPoints.length; i++) {
        this.ctx.lineTo(screenPoints[i].x, screenPoints[i].y)
      }
      this.ctx.stroke()
    }

    this.ctx.restore()
  }

  drawControlPoints(points: Point[]) {
    this.ctx.save()
    this.ctx.fillStyle = '#ffffff'
    this.ctx.strokeStyle = '#ff4444'
    this.ctx.lineWidth = 2

    points.forEach(point => {
      this.ctx.beginPath()
      this.ctx.arc(point.x, point.y, 4, 0, Math.PI * 2)
      this.ctx.fill()
      this.ctx.stroke()
    })

    this.ctx.restore()
  }

  drawAnnotationLabel(annotation: Annotation, position: Point, settings: AnnotationSettings) {
    const label = this.getAnnotationLabel(annotation)
    if (!label) return

    this.ctx.save()
    this.ctx.font = `${settings.fontSize}px Arial`
    this.ctx.fillStyle = '#ffffff'
    this.ctx.strokeStyle = '#000000'
    this.ctx.lineWidth = 1

    const metrics = this.ctx.measureText(label)
    const padding = 4
    const labelWidth = metrics.width + padding * 2
    const labelHeight = settings.fontSize + padding * 2

    // 绘制背景
    this.ctx.fillRect(
      position.x - padding,
      position.y - labelHeight,
      labelWidth,
      labelHeight
    )

    // 绘制边框
    this.ctx.strokeRect(
      position.x - padding,
      position.y - labelHeight,
      labelWidth,
      labelHeight
    )

    // 绘制文字
    this.ctx.fillStyle = '#000000'
    this.ctx.fillText(label, position.x, position.y - padding)

    this.ctx.restore()
  }

  drawRuler(scale: number, offset: Point) {
    const { width, height } = this.canvas
    const rulerSize = 20

    this.ctx.save()
    this.ctx.fillStyle = '#f0f0f0'
    this.ctx.strokeStyle = '#cccccc'
    this.ctx.lineWidth = 1
    this.ctx.font = '10px Arial'
    this.ctx.fillStyle = '#666666'

    // 水平标尺
    this.ctx.fillRect(0, 0, width, rulerSize)
    this.ctx.strokeRect(0, 0, width, rulerSize)

    // 垂直标尺
    this.ctx.fillRect(0, 0, rulerSize, height)
    this.ctx.strokeRect(0, 0, rulerSize, height)

    // 绘制刻度
    const step = 50 * scale
    
    // 水平刻度
    for (let x = offset.x % step; x < width; x += step) {
      if (x > rulerSize) {
        this.ctx.beginPath()
        this.ctx.moveTo(x, 0)
        this.ctx.lineTo(x, rulerSize)
        this.ctx.stroke()

        const value = Math.round((x - offset.x) / scale)
        this.ctx.fillText(value.toString(), x + 2, 12)
      }
    }

    // 垂直刻度
    for (let y = offset.y % step; y < height; y += step) {
      if (y > rulerSize) {
        this.ctx.beginPath()
        this.ctx.moveTo(0, y)
        this.ctx.lineTo(rulerSize, y)
        this.ctx.stroke()

        const value = Math.round((y - offset.y) / scale)
        this.ctx.save()
        this.ctx.translate(12, y - 2)
        this.ctx.rotate(-Math.PI / 2)
        this.ctx.fillText(value.toString(), 0, 0)
        this.ctx.restore()
      }
    }

    this.ctx.restore()
  }

  private getAnnotationColor(type: AnnotationType): string {
    const colors: Record<AnnotationType, string> = {
      'main-question': '#1890ff',
      'sub-question': '#52c41a',
      'answer-area': '#fa8c16',
      'image-area': '#722ed1',
      'question-text': '#eb2f96'
    }
    return colors[type] || '#666666'
  }

  private getAnnotationLabel(annotation: Annotation): string {
    const typeLabels: Record<AnnotationType, string> = {
      'main-question': '大题',
      'sub-question': '小题',
      'answer-area': '答题区域',
      'image-area': '配图区域',
      'question-text': '题干文字'
    }

    const typeLabel = typeLabels[annotation.type] || ''
    const number = annotation.attributes.number || ''
    
    return number ? `${typeLabel}${number}` : typeLabel
  }
}
